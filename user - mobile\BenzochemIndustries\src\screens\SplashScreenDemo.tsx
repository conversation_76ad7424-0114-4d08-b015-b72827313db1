import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { RefreshCw } from 'lucide-react-native';

import { useTheme } from '../contexts/ThemeContext';
import SplashScreen from './SplashScreen';

const SplashScreenDemo: React.FC = () => {
  const { theme } = useTheme();
  const [showSplash, setShowSplash] = useState(false);

  const handleShowSplash = () => {
    setShowSplash(true);
  };

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  if (showSplash) {
    return <SplashScreen onAnimationComplete={handleSplashComplete} />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.foreground }]}>
          SplashScreen Demo
        </Text>
        <Text style={[styles.description, { color: theme.colors.mutedForeground }]}>
          Click the button below to preview the professional splash screen animation.
        </Text>
        
        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.colors.primary }]}
          onPress={handleShowSplash}
          activeOpacity={0.8}
        >
          <RefreshCw size={20} color={theme.colors.primaryForeground} />
          <Text style={[styles.buttonText, { color: theme.colors.primaryForeground }]}>
            Show Splash Screen
          </Text>
        </TouchableOpacity>

        <View style={styles.features}>
          <Text style={[styles.featuresTitle, { color: theme.colors.foreground }]}>
            Features:
          </Text>
          <Text style={[styles.featureItem, { color: theme.colors.mutedForeground }]}>
            • Smooth animations with React Native Animated API
          </Text>
          <Text style={[styles.featureItem, { color: theme.colors.mutedForeground }]}>
            • Professional loading states with progress bar
          </Text>
          <Text style={[styles.featureItem, { color: theme.colors.mutedForeground }]}>
            • Animated logo with rotation and particles
          </Text>
          <Text style={[styles.featureItem, { color: theme.colors.mutedForeground }]}>
            • Theme-aware colors and styling
          </Text>
          <Text style={[styles.featureItem, { color: theme.colors.mutedForeground }]}>
            • Company branding and professional appearance
          </Text>
          <Text style={[styles.featureItem, { color: theme.colors.mutedForeground }]}>
            • Mobile-optimized performance
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '800',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
    marginBottom: 40,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  features: {
    alignSelf: 'stretch',
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  featureItem: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
});

export default SplashScreenDemo;
