import { NavigatorScreenParams } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';

// Tab Navigator Param List
export type TabParamList = {
  Home: undefined;
  Products: { category?: string } | undefined;
  ProductCatalog: { category?: string } | undefined;
  Search: undefined;
  Quotation: undefined;
  Profile: undefined;
  EnhancedProfile: undefined;
};

// Stack Navigator Param List
export type RootStackParamList = {
  Main: NavigatorScreenParams<TabParamList>;
  ProductDetail: { productId: string };
  Auth: undefined;
  Login: undefined;
  Register: undefined;
  WaitingList: undefined;
  Contact: undefined;
  QuotationForm: { initialItems?: any[] } | undefined;
  EnhancedProductCatalog: { category?: string } | undefined;
};

// Navigation Props
export type HomeScreenNavigationProp = BottomTabNavigationProp<TabParamList, 'Home'>;
export type ProductsScreenNavigationProp = BottomTabNavigationProp<TabParamList, 'Products'>;
export type ProductCatalogScreenNavigationProp = BottomTabNavigationProp<TabParamList, 'ProductCatalog'>;
export type SearchScreenNavigationProp = BottomTabNavigationProp<TabParamList, 'Search'>;
export type QuotationScreenNavigationProp = BottomTabNavigationProp<TabParamList, 'Quotation'>;
export type ProfileScreenNavigationProp = BottomTabNavigationProp<TabParamList, 'Profile'>;
export type EnhancedProfileScreenNavigationProp = BottomTabNavigationProp<TabParamList, 'EnhancedProfile'>;

export type ProductDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ProductDetail'>;
export type AuthScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Auth'>;
export type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Login'>;
export type RegisterScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Register'>;
export type WaitingListScreenNavigationProp = StackNavigationProp<RootStackParamList, 'WaitingList'>;
export type ContactScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Contact'>;
export type QuotationFormScreenNavigationProp = StackNavigationProp<RootStackParamList, 'QuotationForm'>;
export type EnhancedProductCatalogScreenNavigationProp = StackNavigationProp<RootStackParamList, 'EnhancedProductCatalog'>;

// Combined navigation prop for components that can navigate to both tab and stack screens
export type AppNavigationProp = StackNavigationProp<RootStackParamList> & BottomTabNavigationProp<TabParamList>;

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}