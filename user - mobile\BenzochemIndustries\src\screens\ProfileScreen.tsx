import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  Switch,
  Modal,
  TextInput,
  RefreshControl,
} from 'react-native';
import {
  User,
  Bell,
  Shield,
  HelpCircle,
  LogOut,
  Edit3,
  ChevronRight,
  Moon,
  Sun,
  Phone,
  Mail,
  Building,
  CheckCircle,
  X,
  Save,
  Eye,
  EyeOff,
  Lock,
  Smartphone,
  Globe,
  FileText,
  Star,
  Package,
  Clock,
  MessageCircle,
  Settings,
  ArrowRight,
  Monitor
} from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { AppNavigationProp } from '../types/navigation';
import LoadingSpinner from '../components/LoadingSpinner';



const ProfileScreen = () => {
  const { theme, setTheme, themeMode } = useTheme();
  const { user, logout, refreshUserData } = useAuth();
  const navigation = useNavigation<AppNavigationProp>();

  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showThemeModal, setShowThemeModal] = useState(false);
  const [editForm, setEditForm] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    phone: user?.phone || '',
    businessName: user?.businessName || '',
  });
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  useEffect(() => {
    if (user) {
      setEditForm({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phone: user.phone || '',
        businessName: user.businessName || '',
      });
    }
  }, [user]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshUserData();
    } catch (error) {
      console.error('Error refreshing user data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive', 
          onPress: async () => {
            try {
              await logout();
              navigation.navigate('Auth');
            } catch (error) {
              Alert.alert('Error', 'Failed to logout');
            }
          }
        },
      ]
    );
  };

  const handleSaveProfile = async () => {
    setLoading(true);
    try {
      // In a real app, you would call an API to update the profile
      Alert.alert('Success', 'Profile updated successfully');
      setShowEditModal(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return;
    }

    setLoading(true);
    try {
      // In a real app, you would call an API to change the password
      Alert.alert('Success', 'Password changed successfully');
      setShowPasswordModal(false);
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return theme.colors.success;
      case 'pending': return theme.colors.warning;
      case 'rejected': return theme.colors.error;
      case 'suspended': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getThemeDisplayInfo = () => {
    switch (themeMode) {
      case 'light':
        return { icon: Sun, title: 'Light Mode', subtitle: 'Always use light theme' };
      case 'dark':
        return { icon: Moon, title: 'Dark Mode', subtitle: 'Always use dark theme' };
      case 'system':
        return { icon: Monitor, title: 'System Theme', subtitle: 'Follow system settings' };
      default:
        return { icon: Monitor, title: 'System Theme', subtitle: 'Follow system settings' };
    }
  };

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    setShowThemeModal(false);
  };

  const renderMinimalHeader = () => {
    const statusColor = getStatusColor(user?.status || 'pending');

    return (
      <View style={[styles.minimalHeader, { backgroundColor: theme.colors.background }]}>
        <View style={styles.profileContainer}>
          <View style={[styles.avatarCircle, { backgroundColor: theme.colors.primary }]}>
            <Text style={[styles.avatarInitials, { color: theme.colors.background }]}>
              {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
            </Text>
          </View>
          
          <View style={styles.profileDetails}>
            <Text style={[styles.profileName, { color: theme.colors.text }]}>
              {user?.firstName}{user?.lastName}
            </Text>
            <Text style={[styles.profileEmail, { color: theme.colors.textSecondary }]}>
              {user?.email}
            </Text>
            <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
          </View>

          <TouchableOpacity
            style={[styles.editIcon, { backgroundColor: theme.colors.surface }]}
            onPress={() => setShowEditModal(true)}
          >
            <Edit3 size={16} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderStatsRow = () => (
    <View style={[styles.statsRow, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.statItem}>
        <Text style={[styles.statNumber, { color: theme.colors.text }]}>0</Text>
        <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Quotations</Text>
      </View>
      <View style={[styles.statDivider, { backgroundColor: theme.colors.border }]} />
      <View style={styles.statItem}>
        <Text style={[styles.statNumber, { color: theme.colors.text }]}>0</Text>
        <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Approved</Text>
      </View>
      <View style={[styles.statDivider, { backgroundColor: theme.colors.border }]} />
      <View style={styles.statItem}>
        <Text style={[styles.statNumber, { color: theme.colors.text }]}>0</Text>
        <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Favorites</Text>
      </View>
    </View>
  );

  const renderBusinessInfo = () => {
    if (!user?.businessName && !user?.gstNumber && !user?.phone) return null;

    return (
      <View style={[styles.infoSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Business</Text>
        
        {user?.businessName && (
          <View style={styles.infoItem}>
            <Building size={18} color={theme.colors.textSecondary} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>{user.businessName}</Text>
          </View>
        )}

        {user?.phone && (
          <View style={styles.infoItem}>
            <Phone size={18} color={theme.colors.textSecondary} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>{user.phone}</Text>
          </View>
        )}

        {user?.gstNumber && (
          <View style={styles.infoItem}>
            <FileText size={18} color={theme.colors.textSecondary} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>{user.gstNumber}</Text>
            {user.isGstVerified && (
              <CheckCircle size={16} color={theme.colors.success} />
            )}
          </View>
        )}
      </View>
    );
  };

  const renderMenuItem = (icon: any, title: string, subtitle?: string, onPress?: () => void, toggle?: boolean, onToggle?: () => void) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={onPress}
      disabled={toggle !== undefined}
    >
      <View style={styles.menuLeft}>
        <View style={[styles.menuIcon, { backgroundColor: theme.colors.surface }]}>
          {React.createElement(icon, { size: 20, color: theme.colors.text })}
        </View>
        <View style={styles.menuContent}>
          <Text style={[styles.menuTitle, { color: theme.colors.text }]}>{title}</Text>
          {subtitle && (
            <Text style={[styles.menuSubtitle, { color: theme.colors.textSecondary }]}>{subtitle}</Text>
          )}
        </View>
      </View>
      
      {toggle !== undefined ? (
        <Switch
          value={toggle}
          onValueChange={onToggle}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary + '40' }}
          thumbColor={toggle ? theme.colors.primary : theme.colors.textSecondary}
        />
      ) : (
        <ChevronRight size={18} color={theme.colors.textSecondary} />
      )}
    </TouchableOpacity>
  );

  const EditProfileModal = () => (
    <Modal
      visible={showEditModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowEditModal(false)}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <SafeAreaView style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Edit Profile</Text>
            <TouchableOpacity onPress={() => setShowEditModal(false)}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>First Name</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
                value={editForm.firstName}
                onChangeText={(text) => setEditForm({ ...editForm, firstName: text })}
                placeholder="Enter first name"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Last Name</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
                value={editForm.lastName}
                onChangeText={(text) => setEditForm({ ...editForm, lastName: text })}
                placeholder="Enter last name"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Phone Number</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
                value={editForm.phone}
                onChangeText={(text) => setEditForm({ ...editForm, phone: text })}
                placeholder="Enter phone number"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Business Name</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
                value={editForm.businessName}
                onChangeText={(text) => setEditForm({ ...editForm, businessName: text })}
                placeholder="Enter business name"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>
          </ScrollView>

          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleSaveProfile}
            disabled={loading}
          >
            {loading ? (
              <LoadingSpinner size="small" color={theme.colors.background} />
            ) : (
              <Text style={[styles.saveButtonText, { color: theme.colors.background }]}>
                Save Changes
              </Text>
            )}
          </TouchableOpacity>
        </SafeAreaView>
      </View>
    </Modal>
  );

  const PasswordModal = () => (
    <Modal
      visible={showPasswordModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowPasswordModal(false)}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <SafeAreaView style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Change Password</Text>
            <TouchableOpacity onPress={() => setShowPasswordModal(false)}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Current Password</Text>
              <View style={[styles.passwordContainer, { backgroundColor: theme.colors.surface }]}>
                <TextInput
                  style={[styles.passwordInput, { color: theme.colors.text }]}
                  value={passwordForm.currentPassword}
                  onChangeText={(text) => setPasswordForm({ ...passwordForm, currentPassword: text })}
                  placeholder="Enter current password"
                  placeholderTextColor={theme.colors.textSecondary}
                  secureTextEntry={!showPasswords.current}
                />
                <TouchableOpacity
                  onPress={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                >
                  {showPasswords.current ? (
                    <EyeOff size={18} color={theme.colors.textSecondary} />
                  ) : (
                    <Eye size={18} color={theme.colors.textSecondary} />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>New Password</Text>
              <View style={[styles.passwordContainer, { backgroundColor: theme.colors.surface }]}>
                <TextInput
                  style={[styles.passwordInput, { color: theme.colors.text }]}
                  value={passwordForm.newPassword}
                  onChangeText={(text) => setPasswordForm({ ...passwordForm, newPassword: text })}
                  placeholder="Enter new password"
                  placeholderTextColor={theme.colors.textSecondary}
                  secureTextEntry={!showPasswords.new}
                />
                <TouchableOpacity
                  onPress={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                >
                  {showPasswords.new ? (
                    <EyeOff size={18} color={theme.colors.textSecondary} />
                  ) : (
                    <Eye size={18} color={theme.colors.textSecondary} />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Confirm Password</Text>
              <View style={[styles.passwordContainer, { backgroundColor: theme.colors.surface }]}>
                <TextInput
                  style={[styles.passwordInput, { color: theme.colors.text }]}
                  value={passwordForm.confirmPassword}
                  onChangeText={(text) => setPasswordForm({ ...passwordForm, confirmPassword: text })}
                  placeholder="Confirm new password"
                  placeholderTextColor={theme.colors.textSecondary}
                  secureTextEntry={!showPasswords.confirm}
                />
                <TouchableOpacity
                  onPress={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                >
                  {showPasswords.confirm ? (
                    <EyeOff size={18} color={theme.colors.textSecondary} />
                  ) : (
                    <Eye size={18} color={theme.colors.textSecondary} />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>

          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleChangePassword}
            disabled={loading}
          >
            {loading ? (
              <LoadingSpinner size="small" color={theme.colors.background} />
            ) : (
              <Text style={[styles.saveButtonText, { color: theme.colors.background }]}>
                Update Password
              </Text>
            )}
          </TouchableOpacity>
        </SafeAreaView>
      </View>
    </Modal>
  );

  const ThemeModal = () => {
    const themeOptions = [
      {
        key: 'light' as const,
        icon: Sun,
        title: 'Light Mode',
        subtitle: 'Always use light theme',
        isSelected: themeMode === 'light',
      },
      {
        key: 'dark' as const,
        icon: Moon,
        title: 'Dark Mode',
        subtitle: 'Always use dark theme',
        isSelected: themeMode === 'dark',
      },
      {
        key: 'system' as const,
        icon: Monitor,
        title: 'System Theme',
        subtitle: 'Follow system settings',
        isSelected: themeMode === 'system',
      },
    ];

    return (
      <Modal
        visible={showThemeModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowThemeModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <SafeAreaView style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Choose Theme</Text>
              <TouchableOpacity onPress={() => setShowThemeModal(false)}>
                <X size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              <Text style={[styles.themeDescription, { color: theme.colors.textSecondary }]}>
                Select your preferred theme. System theme will automatically switch between light and dark based on your device settings.
              </Text>

              <View style={styles.themeOptionsContainer}>
                {themeOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <TouchableOpacity
                      key={option.key}
                      style={[
                        styles.themeOption,
                        {
                          backgroundColor: option.isSelected
                            ? theme.colors.primary + '15'
                            : theme.colors.surface,
                          borderColor: option.isSelected
                            ? theme.colors.primary
                            : theme.colors.border,
                        }
                      ]}
                      onPress={() => handleThemeChange(option.key)}
                      activeOpacity={0.7}
                    >
                      <View style={styles.themeOptionLeft}>
                        <View style={[
                          styles.themeOptionIcon,
                          {
                            backgroundColor: option.isSelected
                              ? theme.colors.primary + '20'
                              : theme.colors.background,
                          }
                        ]}>
                          <IconComponent
                            size={24}
                            color={option.isSelected ? theme.colors.primary : theme.colors.text}
                          />
                        </View>
                        <View style={styles.themeOptionContent}>
                          <Text style={[
                            styles.themeOptionTitle,
                            {
                              color: option.isSelected ? theme.colors.primary : theme.colors.text,
                              fontWeight: option.isSelected ? '600' : '500',
                            }
                          ]}>
                            {option.title}
                          </Text>
                          <Text style={[styles.themeOptionSubtitle, { color: theme.colors.textSecondary }]}>
                            {option.subtitle}
                          </Text>
                        </View>
                      </View>
                      {option.isSelected && (
                        <CheckCircle size={20} color={theme.colors.primary} />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>

              <View style={styles.themePreview}>
                <Text style={[styles.themePreviewTitle, { color: theme.colors.text }]}>
                  Current Theme Preview
                </Text>
                <View style={[styles.themePreviewCard, { backgroundColor: theme.colors.surface }]}>
                  <View style={styles.themePreviewHeader}>
                    <View style={[styles.themePreviewAvatar, { backgroundColor: theme.colors.primary }]}>
                      <Text style={[styles.themePreviewAvatarText, { color: theme.colors.primaryForeground }]}>
                        BC
                      </Text>
                    </View>
                    <View style={styles.themePreviewInfo}>
                      <Text style={[styles.themePreviewName, { color: theme.colors.text }]}>
                        Benzochem Industries
                      </Text>
                      <Text style={[styles.themePreviewEmail, { color: theme.colors.textSecondary }]}>
                        Chemical Products
                      </Text>
                    </View>
                  </View>
                  <View style={styles.themePreviewActions}>
                    <View style={[styles.themePreviewButton, { backgroundColor: theme.colors.primary }]}>
                      <Text style={[styles.themePreviewButtonText, { color: theme.colors.primaryForeground }]}>
                        Primary Button
                      </Text>
                    </View>
                    <View style={[styles.themePreviewButton, {
                      backgroundColor: 'transparent',
                      borderWidth: 1,
                      borderColor: theme.colors.border,
                    }]}>
                      <Text style={[styles.themePreviewButtonText, { color: theme.colors.text }]}>
                        Secondary Button
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </ScrollView>
          </SafeAreaView>
        </View>
      </Modal>
    );
  };

  if (!user) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <SafeAreaView style={styles.emptyState}>
          <View style={[styles.emptyIcon, { backgroundColor: theme.colors.surface }]}>
            <User size={32} color={theme.colors.textSecondary} />
          </View>
          <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>Not Logged In</Text>
          <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
            Please login to access your profile
          </Text>
          <TouchableOpacity
            style={[styles.loginButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => navigation.navigate('Auth')}
          >
            <Text style={[styles.loginButtonText, { color: theme.colors.background }]}>
              Login
            </Text>
          </TouchableOpacity>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar 
        barStyle={theme.dark ? 'light-content' : 'dark-content'} 
        backgroundColor="transparent"
        translucent
      />
      
      <View style={styles.topPadding} />
      <View style={[styles.header, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Profile</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl 
            refreshing={refreshing} 
            onRefresh={onRefresh}
            tintColor={theme.colors.primary}
            colors={[theme.colors.primary]}
          />
        }
      >
        {renderMinimalHeader()}
        {renderStatsRow()}
        {renderBusinessInfo()}

        <View style={styles.menuSection}>
          {(() => {
            const themeInfo = getThemeDisplayInfo();
            return renderMenuItem(themeInfo.icon, 'Theme', themeInfo.subtitle, () => setShowThemeModal(true));
          })()}
          {renderMenuItem(Bell, 'Notifications', 'Manage notifications', () => {})}
          {renderMenuItem(Globe, 'Language', 'English', () => {})}
          {renderMenuItem(Lock, 'Change Password', 'Update your password', () => setShowPasswordModal(true))}
          {renderMenuItem(Smartphone, 'Two-Factor Auth', 'Not enabled', () => {})}
          {renderMenuItem(Shield, 'Privacy', 'Privacy settings', () => {})}
          {renderMenuItem(HelpCircle, 'Help Center', 'Get support', () => {})}
          {renderMenuItem(MessageCircle, 'Contact Us', 'Get in touch', () => {})}
          {renderMenuItem(FileText, 'Terms & Privacy', 'Legal information', () => {})}
        </View>

        <TouchableOpacity
          style={[styles.logoutButton, { backgroundColor: theme.colors.surface }]}
          onPress={handleLogout}
        >
          <LogOut size={20} color={theme.colors.error} />
          <Text style={[styles.logoutText, { color: theme.colors.error }]}>Logout</Text>
        </TouchableOpacity>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      <EditProfileModal />
      <PasswordModal />
      <ThemeModal />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  topPadding: {
    height: 50,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 12,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  scrollView: {
    flex: 1,
  },

  // Minimal Header
  minimalHeader: {
    paddingHorizontal: 24,
    paddingVertical: 24,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarCircle: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarInitials: {
    fontSize: 24,
    fontWeight: '700',
  },
  profileDetails: {
    flex: 1,
    marginLeft: 16,
    position: 'relative',
  },
  profileName: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusDot: {
    position: 'absolute',
    top: 2,
    right: 0,
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  editIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Stats Row
  statsRow: {
    flexDirection: 'row',
    marginHorizontal: 24,
    marginBottom: 24,
    borderRadius: 12,
    paddingVertical: 20,
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  statDivider: {
    width: 1,
    height: 24,
  },

  // Info Section
  infoSection: {
    marginHorizontal: 24,
    marginBottom: 24,
    borderRadius: 12,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  infoText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },

  // Menu Section
  menuSection: {
    marginHorizontal: 24,
    marginBottom: 24,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  menuLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 13,
    fontWeight: '400',
  },

  // Logout Button
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
  },

  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  loginButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  loginButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },

  // Modals
  modalContainer: {
    flex: 1,
  },
  modalContent: {
    flex: 1,
    padding: 24,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  modalBody: {
    flex: 1,
  },

  // Form Inputs
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    fontSize: 16,
    fontWeight: '400',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  passwordInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
  },

  // Save Button
  saveButton: {
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },

  bottomSpacing: {
    height: 120,
  },

  // Theme Modal Styles
  themeDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
    textAlign: 'center',
  },
  themeOptionsContainer: {
    gap: 12,
    marginBottom: 32,
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
  },
  themeOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  themeOptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  themeOptionContent: {
    flex: 1,
  },
  themeOptionTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  themeOptionSubtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  themePreview: {
    marginTop: 8,
  },
  themePreviewTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  themePreviewCard: {
    padding: 20,
    borderRadius: 12,
    gap: 16,
  },
  themePreviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  themePreviewAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  themePreviewAvatarText: {
    fontSize: 16,
    fontWeight: '600',
  },
  themePreviewInfo: {
    flex: 1,
  },
  themePreviewName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  themePreviewEmail: {
    fontSize: 13,
  },
  themePreviewActions: {
    gap: 12,
  },
  themePreviewButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  themePreviewButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ProfileScreen;