import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Animated,
  Dimensions,
  StatusBar,
  ScrollView,
} from 'react-native';
import {
  ArrowLeft,
  Beaker,
  Shield,
  Zap,
  Users,
  ChevronRight,
  Sparkles,
  Award,
  Globe,
  TrendingUp
} from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

import { useTheme } from '../contexts/ThemeContext';
import { AppNavigationProp } from '../types/navigation';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Badge from '../components/ui/Badge';
import { Heading1, Heading2, Heading3, Body1, Body2 } from '../components/ui/Typography';

const logoImage = require('../assets/logo/logo.png');
const { width, height } = Dimensions.get('window');


const AuthScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<AppNavigationProp>();

  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  const navigateToRegister = () => {
    navigation.navigate('Register');
  };

  const goBack = () => {
    navigation.goBack();
  };

  const features = [
    {
      icon: Shield,
      title: "Secure & Trusted",
      description: "ISO certified facility with secure transactions",
      color: theme.colors.success,
    },
    {
      icon: Zap,
      title: "Fast Quotations",
      description: "Get instant quotes for chemical products",
      color: theme.colors.warning,
    },
    {
      icon: Users,
      title: "Expert Support",
      description: "24/7 technical support from experts",
      color: theme.colors.info,
    },
    {
      icon: Globe,
      title: "Global Reach",
      description: "Serving customers worldwide",
      color: theme.colors.primary,
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar
        barStyle={theme.dark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />

      {/* Background Elements */}
      <View style={styles.backgroundElements}>
        <View style={[styles.backgroundCircle, styles.circle1, { backgroundColor: theme.colors.primary + '10' }]} />
        <View style={[styles.backgroundCircle, styles.circle2, { backgroundColor: theme.colors.secondary + '15' }]} />
        <View style={[styles.backgroundCircle, styles.circle3, { backgroundColor: theme.colors.accent + '08' }]} />
      </View>

      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: theme.colors.card }]}
            onPress={goBack}
            activeOpacity={0.7}
          >
            <ArrowLeft size={20} color={theme.colors.foreground} />
          </TouchableOpacity>
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Hero Section */}
          <Animated.View
            style={[
              styles.heroSection,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }
            ]}
          >
            {/* Logo/Brand */}
            <View style={styles.brandContainer}>
              <View style={[styles.logoContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                <Image
                  source={logoImage}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>

              <Heading1 color="foreground" align="center" style={styles.brandTitle}>
                Benzochem Industries
              </Heading1>

              <View style={styles.brandBadgeContainer}>
                <Badge variant="secondary" size="md">
                  <Sparkles size={12} color={theme.colors.mutedForeground} />
                  <Body2 color="mutedForeground" weight="medium" style={styles.badgeText}>
                    Premium Chemical Products
                  </Body2>
                </Badge>
              </View>
            </View>

            {/* Welcome Message */}
            <View style={styles.welcomeContainer}>
              <Heading2 color="foreground" align="center" style={styles.welcomeTitle}>
                Welcome to Our Platform
              </Heading2>
              <Body1 color="mutedForeground" align="center" style={styles.welcomeDescription}>
                Access premium chemical products, request quotations, and manage your business needs with our professional platform.
              </Body1>
            </View>
          </Animated.View>

          {/* Action Buttons */}
          <Animated.View
            style={[
              styles.actionsContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }
            ]}
          >
            <Button
              title="Sign In to Your Account"
              onPress={navigateToLogin}
              variant="primary"
              size="lg"
              fullWidth
              rightIcon={<ChevronRight size={16} color={theme.colors.primaryForeground} />}
              style={styles.primaryButton}
            />

            <Button
              title="Create New Account"
              onPress={navigateToRegister}
              variant="outline"
              size="lg"
              fullWidth
              rightIcon={<ChevronRight size={16} color={theme.colors.primary} />}
              style={styles.secondaryButton}
            />
          </Animated.View>

          {/* Features Grid */}
          <Animated.View
            style={[
              styles.featuresSection,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }
            ]}
          >
            <View style={styles.featuresSectionHeader}>
              <Heading3 color="foreground" align="center">
                Why Choose Benzochem?
              </Heading3>
              <Body2 color="mutedForeground" align="center" style={styles.featuresSubtitle}>
                Trusted by businesses worldwide for quality chemical solutions
              </Body2>
            </View>

            <View style={styles.featuresGrid}>
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <Card key={index} variant="elevated" padding="md" style={styles.featureCard}>
                    <View style={styles.featureContent}>
                      <View style={[styles.featureIcon, { backgroundColor: feature.color + '15' }]}>
                        <Icon size={24} color={feature.color} />
                      </View>
                      <Heading3 color="foreground" style={styles.featureTitle}>
                        {feature.title}
                      </Heading3>
                      <Body2 color="mutedForeground" align="center" style={styles.featureDescription}>
                        {feature.description}
                      </Body2>
                    </View>
                  </Card>
                );
              })}
            </View>
          </Animated.View>

          {/* Stats Section */}
          <Animated.View
            style={[
              styles.statsSection,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }
            ]}
          >
            <Card variant="elevated" padding="lg" style={styles.statsCard}>
              <View style={styles.statsContent}>
                <View style={styles.statItem}>
                  <Heading2 color="primary" style={styles.statValue}>28+</Heading2>
                  <Body2 color="mutedForeground">Years Experience</Body2>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                  <Heading2 color="primary" style={styles.statValue}>500+</Heading2>
                  <Body2 color="mutedForeground">Happy Clients</Body2>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                  <Heading2 color="primary" style={styles.statValue}>ISO</Heading2>
                  <Body2 color="mutedForeground">Certified</Body2>
                </View>
              </View>
            </Card>
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  backgroundCircle: {
    position: 'absolute',
    borderRadius: 9999,
  },
  circle1: {
    width: 200,
    height: 200,
    top: '10%',
    left: '60%',
  },
  circle2: {
    width: 300,
    height: 300,
    bottom: '20%',
    right: '70%',
  },
  circle3: {
    width: 150,
    height: 150,
    top: '40%',
    left: '10%',
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 16,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  heroSection: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  brandContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  logoImage: {
    width: 60,
    height: 60,
  },
  brandTitle: {
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  brandBadgeContainer: {
    marginTop: 8,
  },
  badgeText: {
    marginLeft: 4,
  },
  welcomeContainer: {
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  welcomeTitle: {
    marginBottom: 16,
    letterSpacing: -0.3,
  },
  welcomeDescription: {
    lineHeight: 24,
    textAlign: 'center',
  },
  actionsContainer: {
    marginBottom: 48,
    gap: 16,
  },
  primaryButton: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  secondaryButton: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  featuresSection: {
    marginBottom: 40,
  },
  featuresSectionHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  featuresSubtitle: {
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    justifyContent: 'space-between',
  },
  featureCard: {
    width: (width - 64) / 2,
    minHeight: 140,
  },
  featureContent: {
    alignItems: 'center',
    gap: 12,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureTitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: 12,
    lineHeight: 16,
    textAlign: 'center',
  },
  statsSection: {
    marginBottom: 20,
  },
  statsCard: {
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  statsContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '800',
    marginBottom: 4,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginHorizontal: 16,
  },
});

export default AuthScreen;