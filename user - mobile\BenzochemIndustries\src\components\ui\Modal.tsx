import React from 'react';
import {
  Modal as RNModal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Dimensions,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { X } from 'lucide-react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  style?: ViewStyle;
}

const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnBackdrop = true,
  style,
}) => {
  const { theme } = useTheme();

  const getModalStyle = (): ViewStyle => {
    const sizeStyles = {
      sm: {
        width: Math.min(screenWidth * 0.8, 400),
        maxHeight: screenHeight * 0.6,
      },
      md: {
        width: Math.min(screenWidth * 0.85, 500),
        maxHeight: screenHeight * 0.7,
      },
      lg: {
        width: Math.min(screenWidth * 0.9, 600),
        maxHeight: screenHeight * 0.8,
      },
      xl: {
        width: Math.min(screenWidth * 0.95, 800),
        maxHeight: screenHeight * 0.9,
      },
      full: {
        width: screenWidth,
        height: screenHeight,
        maxHeight: screenHeight,
      },
    };

    return {
      backgroundColor: theme.colors.card,
      borderRadius: size === 'full' ? 0 : theme.borderRadius.lg,
      padding: theme.spacing.lg,
      shadowColor: theme.colors.foreground,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 8,
      ...sizeStyles[size],
    };
  };

  const getOverlayStyle = (): ViewStyle => {
    return {
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    };
  };

  const getTitleStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.semibold,
      fontSize: 20,
      color: theme.colors.foreground,
      marginBottom: theme.spacing.md,
      paddingRight: showCloseButton ? 40 : 0,
    };
  };

  const getCloseButtonStyle = (): ViewStyle => {
    return {
      position: 'absolute',
      top: theme.spacing.lg,
      right: theme.spacing.lg,
      width: 32,
      height: 32,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.muted,
      alignItems: 'center',
      justifyContent: 'center',
    };
  };

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={[styles.overlay, getOverlayStyle()]}>
          <TouchableWithoutFeedback>
            <View style={[getModalStyle(), style]}>
              {showCloseButton && (
                <TouchableOpacity
                  style={getCloseButtonStyle()}
                  onPress={onClose}
                  activeOpacity={0.7}
                >
                  <X size={18} color={theme.colors.mutedForeground} />
                </TouchableOpacity>
              )}
              
              {title && (
                <Text style={getTitleStyle()}>
                  {title}
                </Text>
              )}
              
              <View style={styles.content}>
                {children}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
};

// Modal Header Component
export interface ModalHeaderProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({ children, style }) => {
  const { theme } = useTheme();

  return (
    <View style={[{ marginBottom: theme.spacing.md }, style]}>
      {children}
    </View>
  );
};

// Modal Body Component
export interface ModalBodyProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const ModalBody: React.FC<ModalBodyProps> = ({ children, style }) => {
  return (
    <View style={[styles.body, style]}>
      {children}
    </View>
  );
};

// Modal Footer Component
export interface ModalFooterProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const ModalFooter: React.FC<ModalFooterProps> = ({ children, style }) => {
  const { theme } = useTheme();

  return (
    <View style={[
      {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        gap: theme.spacing.sm,
        marginTop: theme.spacing.lg,
      },
      style
    ]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  content: {
    flex: 1,
  },
  body: {
    flex: 1,
  },
});

export { Modal as default, ModalHeader, ModalBody, ModalFooter };
