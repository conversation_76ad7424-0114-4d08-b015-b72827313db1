import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  style,
  textStyle,
}) => {
  const { theme } = useTheme();

  const getBadgeStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.borderRadius.full,
      alignSelf: 'flex-start',
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
    };

    // Size styles
    const sizeStyles = {
      sm: {
        paddingHorizontal: 6,
        paddingVertical: 2,
        minHeight: 20,
      },
      md: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        minHeight: 24,
      },
      lg: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        minHeight: 32,
      },
    };

    // Variant styles
    const variantStyles = {
      default: {
        backgroundColor: theme.colors.primary,
        borderColor: theme.colors.primary,
      },
      secondary: {
        backgroundColor: theme.colors.secondary,
        borderColor: theme.colors.secondary,
      },
      destructive: {
        backgroundColor: theme.colors.destructive,
        borderColor: theme.colors.destructive,
      },
      outline: {
        backgroundColor: 'transparent',
        borderColor: theme.colors.border,
      },
      success: {
        backgroundColor: theme.colors.success,
        borderColor: theme.colors.success,
      },
      warning: {
        backgroundColor: theme.colors.warning,
        borderColor: theme.colors.warning,
      },
      info: {
        backgroundColor: theme.colors.info,
        borderColor: theme.colors.info,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getTextStyle = (): TextStyle => {
    const baseTextStyle: TextStyle = {
      fontFamily: theme.fonts.medium,
      textAlign: 'center',
      fontSize: 12,
    };

    // Size text styles
    const sizeTextStyles = {
      sm: {
        fontSize: 10,
        lineHeight: 14,
      },
      md: {
        fontSize: 12,
        lineHeight: 16,
      },
      lg: {
        fontSize: 14,
        lineHeight: 18,
      },
    };

    // Variant text styles
    const variantTextStyles = {
      default: {
        color: theme.colors.primaryForeground,
      },
      secondary: {
        color: theme.colors.secondaryForeground,
      },
      destructive: {
        color: theme.colors.destructiveForeground,
      },
      outline: {
        color: theme.colors.foreground,
      },
      success: {
        color: theme.colors.successForeground,
      },
      warning: {
        color: theme.colors.warningForeground,
      },
      info: {
        color: theme.colors.infoForeground,
      },
    };

    return {
      ...baseTextStyle,
      ...sizeTextStyles[size],
      ...variantTextStyles[variant],
    };
  };

  return (
    <View style={[getBadgeStyle(), style]}>
      <Text style={[getTextStyle(), textStyle]}>
        {children}
      </Text>
    </View>
  );
};

export default Badge;
