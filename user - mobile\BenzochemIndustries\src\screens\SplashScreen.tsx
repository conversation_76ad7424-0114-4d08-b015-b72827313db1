import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Image,
  Platform,
} from 'react-native';

import { useTheme } from '../contexts/ThemeContext';

const { width, height } = Dimensions.get('window');
const logoImage = require('../assets/logo/logo.png');

interface SplashScreenProps {
  onAnimationComplete: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onAnimationComplete }) => {
  const { theme } = useTheme();

  // Animation values - Premium brand approach
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const logoScale = useRef(new Animated.Value(0.5)).current;
  const logoTranslateY = useRef(new Animated.Value(30)).current;

  const brandOpacity = useRef(new Animated.Value(0)).current;
  const brandTranslateY = useRef(new Animated.Value(20)).current;

  const taglineOpacity = useRef(new Animated.Value(0)).current;
  const taglineScale = useRef(new Animated.Value(0.9)).current;

  const progressOpacity = useRef(new Animated.Value(0)).current;
  const progressWidth = useRef(new Animated.Value(0)).current;

  const backgroundOpacity = useRef(new Animated.Value(0)).current;

  const [animationPhase, setAnimationPhase] = useState(0);
  const [showProgress, setShowProgress] = useState(false);

  useEffect(() => {
    startPremiumAnimation();
  }, []);

  const startPremiumAnimation = () => {
    // Phase 1: Subtle background fade-in (Nike/Apple style)
    Animated.timing(backgroundOpacity, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Phase 2: Logo entrance with premium timing (1.2s delay like luxury brands)
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: true,
        }),
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 40,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(logoTranslateY, {
          toValue: 0,
          duration: 1200,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setAnimationPhase(1);
        startBrandAnimation();
      });
    }, 400);
  };

  const startBrandAnimation = () => {
    // Phase 3: Brand name with staggered animation (Puma/Adidas style)
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(brandOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(brandTranslateY, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setAnimationPhase(2);
        startTaglineAnimation();
      });
    }, 600);
  };

  const startTaglineAnimation = () => {
    // Phase 4: Tagline with subtle scale (luxury brand approach)
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(taglineOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(taglineScale, {
          toValue: 1,
          tension: 60,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setAnimationPhase(3);
        startProgressAnimation();
      });
    }, 400);
  };

  const startProgressAnimation = () => {
    // Phase 5: Minimal progress indicator (Tesla/Apple style)
    setTimeout(() => {
      setShowProgress(true);
      Animated.timing(progressOpacity, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }).start();

      // Smooth progress animation
      Animated.timing(progressWidth, {
        toValue: 1,
        duration: 1800,
        useNativeDriver: false,
      }).start(() => {
        setTimeout(() => {
          exitAnimation();
        }, 300);
      });
    }, 800);
  };

  const exitAnimation = () => {
    // Premium exit animation (fade out like luxury brands)
    Animated.parallel([
      Animated.timing(backgroundOpacity, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(logoOpacity, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(brandOpacity, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(taglineOpacity, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(progressOpacity, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onAnimationComplete();
    });
  };

  // Premium brand color palette
  const brandColors = {
    primary: '#1a1a1a',
    secondary: '#666666',
    accent: '#0066cc',
    light: '#f8f9fa',
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.dark ? '#000000' : '#ffffff' }]}>
      <StatusBar
        barStyle={theme.dark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent={true}
      />

      {/* Premium Background - Minimal like Apple/Tesla */}
      <Animated.View
        style={[
          styles.backgroundGradient,
          {
            opacity: backgroundOpacity,
            backgroundColor: theme.dark
              ? 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)'
              : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)'
          }
        ]}
      />

      {/* Main Content Container */}
      <View style={styles.contentContainer}>

        {/* Logo Section - Nike/Puma Style Centered */}
        <View style={styles.logoSection}>
          <Animated.View
            style={[
              styles.premiumLogoContainer,
              {
                opacity: logoOpacity,
                transform: [
                  { scale: logoScale },
                  { translateY: logoTranslateY }
                ],
              }
            ]}
          >
            <Image
              source={logoImage}
              style={styles.premiumLogo}
              resizeMode="contain"
            />
          </Animated.View>
        </View>

        {/* Brand Name - Luxury Typography */}
        <Animated.View
          style={[
            styles.brandSection,
            {
              opacity: brandOpacity,
              transform: [{ translateY: brandTranslateY }],
            }
          ]}
        >
          <Text style={[
            styles.premiumBrandName,
            { color: theme.dark ? '#ffffff' : '#000000' }
          ]}>
            BENZOCHEM
          </Text>
          <Text style={[
            styles.premiumBrandSubtitle,
            { color: theme.dark ? '#888888' : '#666666' }
          ]}>
            INDUSTRIES
          </Text>
        </Animated.View>

        {/* Tagline - Minimal like luxury brands */}
        <Animated.View
          style={[
            styles.taglineSection,
            {
              opacity: taglineOpacity,
              transform: [{ scale: taglineScale }],
            }
          ]}
        >
          <Text style={[
            styles.premiumTagline,
            { color: theme.dark ? '#cccccc' : '#333333' }
          ]}>
            Excellence in Chemical Innovation
          </Text>
        </Animated.View>

        {/* Progress Indicator - Tesla/Apple Style */}
        {showProgress && (
          <Animated.View
            style={[
              styles.progressSection,
              { opacity: progressOpacity }
            ]}
          >
            <View style={[
              styles.premiumProgressContainer,
              { backgroundColor: theme.dark ? '#333333' : '#e0e0e0' }
            ]}>
              <Animated.View
                style={[
                  styles.premiumProgressBar,
                  {
                    backgroundColor: theme.dark ? '#ffffff' : '#000000',
                    width: progressWidth.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    }),
                  }
                ]}
              />
            </View>
          </Animated.View>
        )}

      </View>

      {/* Minimal Footer - Like premium brands */}
      <View style={styles.premiumFooter}>
        <Text style={[
          styles.premiumFooterText,
          { color: theme.dark ? '#666666' : '#999999' }
        ]}>
          Est. 2025
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Premium Container - Full screen like luxury brands
  container: {
    flex: 1,
    position: 'relative',
  },

  // Background - Minimal gradient like Apple/Tesla
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },

  // Content Container - Centered like Nike/Puma
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
  },

  // Logo Section - Premium spacing
  logoSection: {
    alignItems: 'center',
    marginBottom: 60,
  },

  // Premium Logo Container - Clean like luxury brands
  premiumLogoContainer: {
    width: 140,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    // Subtle shadow like premium brands
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.1,
        shadowRadius: 20,
      },
      android: {
        elevation: 8,
      },
    }),
  },

  // Premium Logo - Larger and cleaner
  premiumLogo: {
    width: 100,
    height: 100,
  },

  // Brand Section - Typography focused
  brandSection: {
    alignItems: 'center',
    marginBottom: 40,
  },

  // Premium Brand Name - Bold like Nike/Adidas
  premiumBrandName: {
    fontSize: 42,
    fontWeight: '900',
    letterSpacing: 2,
    textAlign: 'center',
    marginBottom: 8,
    // Premium font family if available
    fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'Roboto',
  },

  // Premium Brand Subtitle - Refined
  premiumBrandSubtitle: {
    fontSize: 16,
    fontWeight: '300',
    letterSpacing: 4,
    textAlign: 'center',
    // Light font weight like luxury brands
    fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'Roboto',
  },

  // Tagline Section - Minimal spacing
  taglineSection: {
    alignItems: 'center',
    marginBottom: 80,
  },

  // Premium Tagline - Elegant like luxury brands
  premiumTagline: {
    fontSize: 14,
    fontWeight: '400',
    letterSpacing: 1,
    textAlign: 'center',
    fontStyle: 'italic',
    opacity: 0.8,
    fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'Roboto',
  },

  // Progress Section - Minimal like Tesla/Apple
  progressSection: {
    alignItems: 'center',
    width: '100%',
  },

  // Premium Progress Container - Thin and elegant
  premiumProgressContainer: {
    width: 200,
    height: 2,
    borderRadius: 1,
    overflow: 'hidden',
  },

  // Premium Progress Bar - Smooth animation
  premiumProgressBar: {
    height: '100%',
    borderRadius: 1,
  },

  // Premium Footer - Minimal like luxury brands
  premiumFooter: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 60 : 40,
    alignSelf: 'center',
  },

  // Premium Footer Text - Subtle
  premiumFooterText: {
    fontSize: 12,
    fontWeight: '300',
    letterSpacing: 1,
    opacity: 0.6,
    fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'Roboto',
  },
});

export default SplashScreen;
