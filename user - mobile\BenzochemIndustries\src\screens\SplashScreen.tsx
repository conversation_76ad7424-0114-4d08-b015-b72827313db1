import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Image,
  Easing,
} from 'react-native';
import { 
  Beaker, 
  Sparkles, 
  Shield, 
  Award,
  TrendingUp,
  Globe,
  CheckCircle
} from 'lucide-react-native';

import { useTheme } from '../contexts/ThemeContext';

const { width, height } = Dimensions.get('window');
const logoImage = require('../assets/logo/logo.png');

interface SplashScreenProps {
  onAnimationComplete: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onAnimationComplete }) => {
  const { theme } = useTheme();
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const logoRotateAnim = useRef(new Animated.Value(0)).current;
  const particleAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  
  const [currentStep, setCurrentStep] = useState(0);
  const [showContent, setShowContent] = useState(false);

  const loadingSteps = [
    { text: 'Initializing Application...', icon: Beaker, color: '#10B981' },
    { text: 'Loading Product Catalog...', icon: Globe, color: '#3B82F6' },
    { text: 'Securing Connection...', icon: Shield, color: '#F59E0B' },
    { text: 'Welcome to Benzochem!', icon: CheckCircle, color: '#10B981' },
  ];

  useEffect(() => {
    startAnimation();
  }, []);

  const startAnimation = () => {
    // Initial logo animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
        easing: Easing.out(Easing.back(1.2)),
      }),
    ]).start(() => {
      setShowContent(true);
      startContentAnimation();
    });

    // Logo rotation animation
    Animated.loop(
      Animated.timing(logoRotateAnim, {
        toValue: 1,
        duration: 8000,
        useNativeDriver: true,
        easing: Easing.linear,
      })
    ).start();

    // Particle animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.sine),
        }),
        Animated.timing(particleAnim, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.sine),
        }),
      ])
    ).start();
  };

  const startContentAnimation = () => {
    // Progress animation with steps
    const stepDuration = 600;
    
    loadingSteps.forEach((_, index) => {
      setTimeout(() => {
        setCurrentStep(index);
        
        Animated.timing(progressAnim, {
          toValue: (index + 1) / loadingSteps.length,
          duration: stepDuration,
          useNativeDriver: false,
          easing: Easing.out(Easing.cubic),
        }).start();
        
        if (index === loadingSteps.length - 1) {
          setTimeout(() => {
            exitAnimation();
          }, 800);
        }
      }, index * stepDuration);
    });
  };

  const exitAnimation = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
        easing: Easing.in(Easing.cubic),
      }),
      Animated.timing(scaleAnim, {
        toValue: 1.2,
        duration: 500,
        useNativeDriver: true,
        easing: Easing.in(Easing.cubic),
      }),
    ]).start(() => {
      onAnimationComplete();
    });
  };

  const logoRotation = logoRotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const particleOpacity = particleAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 1, 0.3],
  });

  const particleScale = particleAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.8, 1.2, 0.8],
  });

  const currentStepData = loadingSteps[currentStep];
  const StepIcon = currentStepData?.icon || Beaker;

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar 
        barStyle={theme.dark ? 'light-content' : 'dark-content'} 
        backgroundColor={theme.colors.background}
        translucent={false}
      />
      
      {/* Background Elements */}
      <View style={styles.backgroundElements}>
        {/* Animated particles */}
        {[...Array(6)].map((_, index) => (
          <Animated.View
            key={index}
            style={[
              styles.particle,
              {
                backgroundColor: theme.colors.primary + '20',
                opacity: particleOpacity,
                transform: [{ scale: particleScale }],
                top: `${20 + index * 15}%`,
                left: `${10 + index * 12}%`,
              }
            ]}
          />
        ))}
        
        {/* Gradient circles */}
        <View style={[styles.gradientCircle, styles.circle1, { backgroundColor: theme.colors.primary + '10' }]} />
        <View style={[styles.gradientCircle, styles.circle2, { backgroundColor: theme.colors.secondary + '15' }]} />
        <View style={[styles.gradientCircle, styles.circle3, { backgroundColor: theme.colors.accent + '08' }]} />
      </View>

      {/* Main Content */}
      <Animated.View 
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim }
            ],
          }
        ]}
      >
        {/* Logo Section */}
        <View style={styles.logoSection}>
          <Animated.View 
            style={[
              styles.logoContainer,
              { 
                backgroundColor: theme.colors.primary + '15',
                transform: [{ rotate: logoRotation }]
              }
            ]}
          >
            <Image 
              source={logoImage}
              style={styles.logoImage}
              resizeMode="contain"
            />
          </Animated.View>
          
          <View style={styles.brandContainer}>
            <Text style={[styles.brandTitle, { color: theme.colors.foreground }]}>
              Benzochem Industries
            </Text>
            <View style={styles.brandBadge}>
              <Sparkles size={12} color={theme.colors.primary} />
              <Text style={[styles.brandSubtitle, { color: theme.colors.mutedForeground }]}>
                Premium Chemical Products
              </Text>
            </View>
            <Text style={[styles.tagline, { color: theme.colors.mutedForeground }]}>
              "Excellence in Chemical Innovation"
            </Text>
          </View>
        </View>

        {/* Loading Section */}
        {showContent && (
          <Animated.View style={styles.loadingSection}>
            {/* Progress Bar */}
            <View style={[styles.progressContainer, { backgroundColor: theme.colors.muted }]}>
              <Animated.View 
                style={[
                  styles.progressBar,
                  { 
                    backgroundColor: theme.colors.primary,
                    width: progressAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    }),
                  }
                ]}
              />
            </View>
            
            {/* Loading Step */}
            <View style={styles.stepContainer}>
              <View style={[styles.stepIcon, { backgroundColor: (currentStepData?.color || theme.colors.primary) + '15' }]}>
                <StepIcon size={16} color={currentStepData?.color || theme.colors.primary} />
              </View>
              <Text style={[styles.stepText, { color: theme.colors.foreground }]}>
                {currentStepData?.text}
              </Text>
            </View>
          </Animated.View>
        )}

        {/* Features */}
        <View style={styles.featuresContainer}>
          <View style={styles.feature}>
            <Shield size={16} color={theme.colors.success} />
            <Text style={[styles.featureText, { color: theme.colors.mutedForeground }]}>
              ISO Certified
            </Text>
          </View>
          <View style={styles.feature}>
            <Award size={16} color={theme.colors.warning} />
            <Text style={[styles.featureText, { color: theme.colors.mutedForeground }]}>
              28+ Years
            </Text>
          </View>
          <View style={styles.feature}>
            <TrendingUp size={16} color={theme.colors.info} />
            <Text style={[styles.featureText, { color: theme.colors.mutedForeground }]}>
              500+ Clients
            </Text>
          </View>
        </View>
      </Animated.View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.colors.mutedForeground }]}>
          Powered by Innovation
        </Text>
        <Text style={[styles.versionText, { color: theme.colors.mutedForeground }]}>
          v1.0.0
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  backgroundElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  particle: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  gradientCircle: {
    position: 'absolute',
    borderRadius: 9999,
  },
  circle1: {
    width: 300,
    height: 300,
    top: '10%',
    right: '20%',
  },
  circle2: {
    width: 200,
    height: 200,
    bottom: '15%',
    left: '10%',
  },
  circle3: {
    width: 150,
    height: 150,
    top: '50%',
    left: '70%',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoSection: {
    alignItems: 'center',
    marginBottom: 60,
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
  },
  logoImage: {
    width: 80,
    height: 80,
  },
  brandContainer: {
    alignItems: 'center',
  },
  brandTitle: {
    fontSize: 32,
    fontWeight: '800',
    letterSpacing: -1,
    marginBottom: 12,
    textAlign: 'center',
  },
  brandBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  brandSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  loadingSection: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 40,
  },
  progressContainer: {
    width: '80%',
    height: 4,
    borderRadius: 2,
    marginBottom: 20,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  stepIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepText: {
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.3,
  },
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 20,
  },
  feature: {
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 12,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  footer: {
    position: 'absolute',
    bottom: 60,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    letterSpacing: 0.5,
  },
  versionText: {
    fontSize: 12,
    fontWeight: '400',
    opacity: 0.7,
  },
});

export default SplashScreen;
