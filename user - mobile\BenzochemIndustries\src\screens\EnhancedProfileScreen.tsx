import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { useQuotation } from '../contexts/QuotationContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import { AppNavigationProp } from '../types/navigation';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { Form, FormField, FormLabel, FormMessage, FormSection, FormActions } from '../components/ui/Form';
import Card, { CardContent, CardHeader } from '../components/ui/Card';
import { Typography, Heading2, Heading3, Body1, Body2 } from '../components/ui/Typography';
import Badge from '../components/ui/Badge';
import {
  User,
  Mail,
  Phone,
  Building,
  Settings,
  Bell,
  Shield,
  HelpCircle,
  LogOut,
  Edit3,
  TrendingUp,
  CheckCircle2,
  AlertCircle,
  RefreshCw,
  Award,
  Clock,
  FileText,
  Package,
  CreditCard,
  Heart,
  Globe,
  Lock,
  Smartphone,
  MessageCircle,
  Sun,
  Moon,
  ChevronRight,
} from 'lucide-react-native';

type TabType = 'overview' | 'profile' | 'business' | 'quotations' | 'settings';

interface ProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  address1: string;
  address2: string;
  city: string;
  province: string;
  country: string;
  zip: string;
}

const EnhancedProfileScreen: React.FC = () => {
  const { theme, isDark, toggleTheme } = useTheme();
  const navigation = useNavigation<AppNavigationProp>();
  const { user, logout, refreshUserData, isLoading } = useAuth();
  const { quotations } = useQuotation();

  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateStatus, setUpdateStatus] = useState<'success' | 'error' | null>(null);
  const [updateMessage, setUpdateMessage] = useState('');

  const [formData, setFormData] = useState<ProfileFormData>({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    company: user?.businessName || '',
    address1: '',
    address2: '',
    city: '',
    province: '',
    country: 'India',
    zip: '',
  });

  // Update form data when user data changes
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phone: user.phone || '',
        company: user.businessName || '',
        address1: '',
        address2: '',
        city: '',
        province: '',
        country: 'India',
        zip: '',
      });
    }
  }, [user]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshUserData();
    } catch (error) {
      console.error('Error refreshing user data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear status when user starts editing
    if (updateStatus) {
      setUpdateStatus(null);
      setUpdateMessage('');
    }
  };

  const handleSubmit = async () => {
    setIsUpdating(true);
    setUpdateStatus(null);
    setUpdateMessage('');

    try {
      // TODO: Implement API call to update user profile
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUpdateStatus('success');
      setUpdateMessage('Profile updated successfully!');
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      setUpdateStatus('error');
      setUpdateMessage('Failed to update profile. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            logout();
            navigation.navigate('Login');
          },
        },
      ]
    );
  };

  // Calculate stats
  const pendingQuotations = quotations?.filter(q => q.status === 'pending' || q.status === 'submitted').length || 0;
  const approvedQuotations = quotations?.filter(q => q.status === 'approved').length || 0;
  const totalQuotations = quotations?.length || 0;

  const menuItems = [
    { id: 'overview', label: 'Overview', icon: TrendingUp },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'business', label: 'Business', icon: Building },
    { id: 'quotations', label: 'Quotations', icon: FileText },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  const renderTabNavigation = () => (
    <Card variant="elevated" padding="md" style={styles.tabCard}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabScrollContainer}>
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeTab === item.id;
          
          return (
            <TouchableOpacity
              key={item.id}
              style={[
                styles.tabButton,
                {
                  backgroundColor: isActive ? theme.colors.primary : 'transparent',
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => setActiveTab(item.id as TabType)}
              activeOpacity={0.7}
            >
              <Icon 
                size={18} 
                color={isActive ? theme.colors.primaryForeground : theme.colors.mutedForeground} 
              />
              <Text style={[
                styles.tabButtonText,
                { 
                  color: isActive ? theme.colors.primaryForeground : theme.colors.mutedForeground 
                }
              ]}>
                {item.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </Card>
  );

  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        <Card variant="elevated" padding="md" style={styles.statCard}>
          <View style={styles.statContent}>
            <View style={[styles.statIcon, { backgroundColor: theme.colors.primary + '15' }]}>
              <Clock size={20} color={theme.colors.primary} />
            </View>
            <Heading3 color="primary" style={styles.statValue}>{pendingQuotations}</Heading3>
            <Body2 color="mutedForeground">Pending Quotations</Body2>
          </View>
        </Card>

        <Card variant="elevated" padding="md" style={styles.statCard}>
          <View style={styles.statContent}>
            <View style={[styles.statIcon, { backgroundColor: theme.colors.success + '15' }]}>
              <CheckCircle2 size={20} color={theme.colors.success} />
            </View>
            <Heading3 color="success" style={styles.statValue}>{approvedQuotations}</Heading3>
            <Body2 color="mutedForeground">Approved Quotes</Body2>
          </View>
        </Card>

        <Card variant="elevated" padding="md" style={styles.statCard}>
          <View style={styles.statContent}>
            <View style={[styles.statIcon, { backgroundColor: theme.colors.info + '15' }]}>
              <FileText size={20} color={theme.colors.info} />
            </View>
            <Heading3 color="info" style={styles.statValue}>{totalQuotations}</Heading3>
            <Body2 color="mutedForeground">Total Requests</Body2>
          </View>
        </Card>
      </View>

      {/* Account Status */}
      <Card variant="elevated" padding="lg" style={styles.statusCard}>
        <View style={styles.statusHeader}>
          <View style={styles.statusInfo}>
            <Heading3 color="foreground">Account Status</Heading3>
            <Badge 
              variant={user?.status === 'approved' ? 'success' : user?.status === 'pending' ? 'warning' : 'destructive'}
              size="md"
            >
              {user?.status === 'approved' ? 'Active' : user?.status === 'pending' ? 'Pending' : 'Inactive'}
            </Badge>
          </View>
          <TouchableOpacity onPress={handleRefresh} disabled={isRefreshing}>
            <RefreshCw 
              size={20} 
              color={theme.colors.primary} 
              style={isRefreshing ? { transform: [{ rotate: '180deg' }] } : {}}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.userInfo}>
          <View style={styles.userInfoItem}>
            <User size={16} color={theme.colors.mutedForeground} />
            <Body1 color="foreground">{user?.firstName} {user?.lastName}</Body1>
          </View>
          <View style={styles.userInfoItem}>
            <Mail size={16} color={theme.colors.mutedForeground} />
            <Body1 color="foreground">{user?.email}</Body1>
          </View>
          {user?.phone && (
            <View style={styles.userInfoItem}>
              <Phone size={16} color={theme.colors.mutedForeground} />
              <Body1 color="foreground">{user.phone}</Body1>
            </View>
          )}
          {user?.businessName && (
            <View style={styles.userInfoItem}>
              <Building size={16} color={theme.colors.mutedForeground} />
              <Body1 color="foreground">{user.businessName}</Body1>
            </View>
          )}
        </View>

        {user?.gstNumber && (
          <View style={styles.gstInfo}>
            <View style={[styles.gstBadge, { backgroundColor: theme.colors.success + '15' }]}>
              <Award size={16} color={theme.colors.success} />
              <Body2 color="success" weight="medium">GST Verified</Body2>
            </View>
            <Body2 color="mutedForeground">GST: {user.gstNumber}</Body2>
          </View>
        )}
      </Card>

      {/* Quick Actions */}
      <Card variant="elevated" padding="lg" style={styles.actionsCard}>
        <Heading3 color="foreground" style={styles.actionsTitle}>Quick Actions</Heading3>
        <View style={styles.actionButtons}>
          <Button
            title="Request Quote"
            onPress={() => navigation.navigate('QuotationForm')}
            variant="primary"
            size="md"
            leftIcon={<FileText size={16} color={theme.colors.primaryForeground} />}
            style={styles.actionButton}
          />
          <Button
            title="View Products"
            onPress={() => navigation.navigate('ProductCatalog')}
            variant="outline"
            size="md"
            leftIcon={<Package size={16} color={theme.colors.primary} />}
            style={styles.actionButton}
          />
        </View>
        <Button
          title="Contact Support"
          onPress={() => navigation.navigate('Contact')}
          variant="ghost"
          size="md"
          leftIcon={<MessageCircle size={16} color={theme.colors.mutedForeground} />}
          style={styles.supportButton}
        />
      </Card>
    </View>
  );

  const renderProfileTab = () => (
    <View style={styles.tabContent}>
      <Card variant="elevated" padding="lg" style={styles.profileCard}>
        <View style={styles.profileHeader}>
          <Heading3 color="foreground">Personal Information</Heading3>
          <View style={styles.profileActions}>
            {!isEditing ? (
              <Button
                title="Edit"
                onPress={() => setIsEditing(true)}
                variant="outline"
                size="sm"
                leftIcon={<Edit3 size={14} color={theme.colors.primary} />}
              />
            ) : (
              <View style={styles.editActions}>
                <Button
                  title="Cancel"
                  onPress={() => {
                    setIsEditing(false);
                    setUpdateStatus(null);
                    setUpdateMessage('');
                  }}
                  variant="ghost"
                  size="sm"
                />
                <Button
                  title="Save"
                  onPress={handleSubmit}
                  loading={isUpdating}
                  disabled={isUpdating}
                  variant="primary"
                  size="sm"
                />
              </View>
            )}
          </View>
        </View>

        {/* Status Messages */}
        {updateStatus && (
          <View style={[
            styles.statusMessage,
            { 
              backgroundColor: updateStatus === 'success' 
                ? theme.colors.success + '15' 
                : theme.colors.destructive + '15',
              borderColor: updateStatus === 'success' 
                ? theme.colors.success 
                : theme.colors.destructive,
            }
          ]}>
            {updateStatus === 'success' ? (
              <CheckCircle2 size={16} color={theme.colors.success} />
            ) : (
              <AlertCircle size={16} color={theme.colors.destructive} />
            )}
            <Text style={[
              styles.statusMessageText,
              { 
                color: updateStatus === 'success' 
                  ? theme.colors.success 
                  : theme.colors.destructive 
              }
            ]}>
              {updateMessage}
            </Text>
          </View>
        )}

        <Form>
          <FormSection>
            {/* Name Fields */}
            <View style={styles.row}>
              <FormField style={styles.halfField}>
                <FormLabel>First Name</FormLabel>
                <Input
                  value={formData.firstName}
                  onChangeText={(value) => handleInputChange('firstName', value)}
                  placeholder="Enter your first name"
                  disabled={!isEditing}
                  leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
                />
              </FormField>

              <FormField style={styles.halfField}>
                <FormLabel>Last Name</FormLabel>
                <Input
                  value={formData.lastName}
                  onChangeText={(value) => handleInputChange('lastName', value)}
                  placeholder="Enter your last name"
                  disabled={!isEditing}
                  leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
                />
              </FormField>
            </View>

            {/* Email Field */}
            <FormField>
              <FormLabel>Email Address</FormLabel>
              <Input
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                placeholder="Enter your email address"
                keyboardType="email-address"
                autoCapitalize="none"
                disabled={!isEditing}
                leftIcon={<Mail size={18} color={theme.colors.mutedForeground} />}
              />
            </FormField>

            {/* Phone Field */}
            <FormField>
              <FormLabel>Phone Number</FormLabel>
              <Input
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                placeholder="Enter your phone number"
                keyboardType="phone-pad"
                disabled={!isEditing}
                leftIcon={<Phone size={18} color={theme.colors.mutedForeground} />}
              />
            </FormField>

            {/* Company Field */}
            <FormField>
              <FormLabel>Company</FormLabel>
              <Input
                value={formData.company}
                onChangeText={(value) => handleInputChange('company', value)}
                placeholder="Enter your company name"
                disabled={!isEditing}
                leftIcon={<Building size={18} color={theme.colors.mutedForeground} />}
              />
            </FormField>
          </FormSection>
        </Form>
      </Card>
    </View>
  );

  const renderBusinessTab = () => (
    <View style={styles.tabContent}>
      <Card variant="elevated" padding="lg" style={styles.businessCard}>
        <Heading3 color="foreground" style={styles.businessTitle}>Business Information</Heading3>
        <Body2 color="mutedForeground" style={styles.businessDescription}>
          View your registered business details
        </Body2>

        {user?.gstNumber ? (
          <View style={styles.businessInfo}>
            <View style={styles.businessField}>
              <Body2 color="mutedForeground" style={styles.fieldLabel}>Trade Name</Body2>
              <View style={[styles.fieldValue, { backgroundColor: theme.colors.muted + '30' }]}>
                <Body1 color="foreground">{user.businessName || 'Not provided'}</Body1>
              </View>
            </View>

            <View style={styles.businessField}>
              <Body2 color="mutedForeground" style={styles.fieldLabel}>GST Number</Body2>
              <View style={[styles.fieldValue, { backgroundColor: theme.colors.muted + '30' }]}>
                <Body1 color="foreground">{user.gstNumber}</Body1>
                <Badge variant="success" size="sm">Verified</Badge>
              </View>
            </View>

            {user.legalNameOfBusiness && (
              <View style={styles.businessField}>
                <Body2 color="mutedForeground" style={styles.fieldLabel}>Legal Name of Business</Body2>
                <View style={[styles.fieldValue, { backgroundColor: theme.colors.muted + '30' }]}>
                  <Body1 color="foreground">{user.legalNameOfBusiness}</Body1>
                </View>
              </View>
            )}

            {user.principalPlaceOfBusiness && (
              <View style={styles.businessField}>
                <Body2 color="mutedForeground" style={styles.fieldLabel}>Business Address</Body2>
                <View style={[styles.fieldValue, { backgroundColor: theme.colors.muted + '30' }]}>
                  <Body1 color="foreground">{user.principalPlaceOfBusiness}</Body1>
                </View>
              </View>
            )}
          </View>
        ) : (
          <View style={styles.noBusinessInfo}>
            <Building size={48} color={theme.colors.mutedForeground} />
            <Heading3 color="mutedForeground" style={styles.noBusinessTitle}>
              No Business Information
            </Heading3>
            <Body1 color="mutedForeground" align="center" style={styles.noBusinessDescription}>
              Complete your business registration to access premium features.
            </Body1>
          </View>
        )}
      </Card>
    </View>
  );

  const renderSettingsTab = () => (
    <View style={styles.tabContent}>
      <Card variant="elevated" padding="lg" style={styles.settingsCard}>
        <Heading3 color="foreground" style={styles.settingsTitle}>Settings</Heading3>

        <View style={styles.settingsList}>
          {/* Theme Toggle */}
          <TouchableOpacity
            style={[styles.settingItem, { borderColor: theme.colors.border }]}
            onPress={toggleTheme}
            activeOpacity={0.7}
          >
            <View style={styles.settingInfo}>
              {isDark ? (
                <Moon size={20} color={theme.colors.mutedForeground} />
              ) : (
                <Sun size={20} color={theme.colors.mutedForeground} />
              )}
              <View style={styles.settingText}>
                <Body1 color="foreground">Dark Mode</Body1>
                <Body2 color="mutedForeground">{isDark ? 'Enabled' : 'Disabled'}</Body2>
              </View>
            </View>
            <Badge variant={isDark ? 'success' : 'secondary'} size="sm">
              {isDark ? 'On' : 'Off'}
            </Badge>
          </TouchableOpacity>

          {/* Help */}
          <TouchableOpacity
            style={[styles.settingItem, { borderColor: theme.colors.border }]}
            onPress={() => navigation.navigate('Contact')}
            activeOpacity={0.7}
          >
            <View style={styles.settingInfo}>
              <HelpCircle size={20} color={theme.colors.mutedForeground} />
              <View style={styles.settingText}>
                <Body1 color="foreground">Help Center</Body1>
                <Body2 color="mutedForeground">Get support</Body2>
              </View>
            </View>
            <ChevronRight size={16} color={theme.colors.mutedForeground} />
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <Button
          title="Sign Out"
          onPress={handleLogout}
          variant="destructive"
          size="lg"
          leftIcon={<LogOut size={16} color={theme.colors.destructiveForeground} />}
          style={styles.logoutButton}
        />
      </Card>
    </View>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewTab();
      case 'profile':
        return renderProfileTab();
      case 'business':
        return renderBusinessTab();
      case 'settings':
        return renderSettingsTab();
      default:
        return renderOverviewTab();
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.mutedForeground }]}>
          Loading profile...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={[styles.avatarContainer, { backgroundColor: theme.colors.primary + '15' }]}>
              <User size={32} color={theme.colors.primary} />
            </View>
            <View style={styles.headerInfo}>
              <Heading2 color="foreground">
                {user?.firstName} {user?.lastName}
              </Heading2>
              <Body1 color="mutedForeground">{user?.email}</Body1>
              <Badge
                variant={user?.status === 'approved' ? 'success' : user?.status === 'pending' ? 'warning' : 'destructive'}
                size="sm"
                style={styles.statusBadge}
              >
                {user?.status === 'approved' ? 'Active' : user?.status === 'pending' ? 'Pending' : 'Inactive'}
              </Badge>
            </View>
          </View>
        </View>

        {/* Tab Navigation */}
        {renderTabNavigation()}

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'System',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
    marginTop: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  avatarContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerInfo: {
    flex: 1,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  tabCard: {
    marginBottom: 16,
  },
  tabScrollContainer: {
    gap: 8,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    gap: 8,
    minWidth: 100,
  },
  tabButtonText: {
    fontSize: 14,
    fontFamily: 'System',
    fontWeight: '500',
  },
  tabContent: {
    gap: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
  },
  statContent: {
    alignItems: 'center',
    gap: 8,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statusCard: {
    marginBottom: 16,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  userInfo: {
    gap: 12,
    marginBottom: 16,
  },
  userInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  gstInfo: {
    alignItems: 'center',
    gap: 8,
  },
  gstBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  actionsCard: {
    marginBottom: 16,
  },
  actionsTitle: {
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  actionButton: {
    flex: 1,
  },
  supportButton: {
    alignSelf: 'center',
  },
  profileCard: {
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileActions: {
    flexDirection: 'row',
    gap: 8,
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  statusMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  statusMessageText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'System',
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfField: {
    flex: 1,
  },
  businessCard: {
    marginBottom: 16,
  },
  businessTitle: {
    marginBottom: 8,
  },
  businessDescription: {
    marginBottom: 20,
  },
  businessInfo: {
    gap: 16,
  },
  businessField: {
    gap: 6,
  },
  fieldLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  fieldValue: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  noBusinessInfo: {
    alignItems: 'center',
    gap: 16,
    paddingVertical: 32,
  },
  noBusinessTitle: {
    marginTop: 8,
  },
  noBusinessDescription: {
    textAlign: 'center',
    lineHeight: 22,
  },
  settingsCard: {
    marginBottom: 16,
  },
  settingsTitle: {
    marginBottom: 20,
  },
  settingsList: {
    gap: 0,
    marginBottom: 24,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  settingText: {
    flex: 1,
  },
  logoutButton: {
    marginTop: 8,
  },
});

export default EnhancedProfileScreen;
