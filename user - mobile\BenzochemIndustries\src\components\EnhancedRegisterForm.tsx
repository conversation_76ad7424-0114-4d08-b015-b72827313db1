import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import Button from './ui/Button';
import Input from './ui/Input';
import { Form, FormField, FormLabel, FormMessage, FormSection, FormActions, FormSeparator } from './ui/Form';
import Card from './ui/Card';
import { Typography, Heading2, Body1, Body2 } from './ui/Typography';
import { Mail, Lock, User, Building, Phone, Eye, EyeOff, FileText } from 'lucide-react-native';

interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  businessName: string;
  gstNumber: string;
  agreedToEmailMarketing: boolean;
  agreedToSmsMarketing: boolean;
}

interface RegisterFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  phone?: string;
  gstNumber?: string;
}

interface EnhancedRegisterFormProps {
  onLogin?: () => void;
  onSuccess?: () => void;
}

const EnhancedRegisterForm: React.FC<EnhancedRegisterFormProps> = ({
  onLogin,
  onSuccess,
}) => {
  const { theme } = useTheme();
  const { register } = useAuth();
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    businessName: '',
    gstNumber: '',
    agreedToEmailMarketing: false,
    agreedToSmsMarketing: false,
  });
  const [errors, setErrors] = useState<RegisterFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [gstVerificationStatus, setGstVerificationStatus] = useState<'idle' | 'verifying' | 'verified' | 'error'>('idle');

  const validateForm = (): boolean => {
    const newErrors: RegisterFormErrors = {};

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!enhancedApiClient.validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Phone validation (optional but if provided, must be valid)
    if (formData.phone.trim() && !enhancedApiClient.validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // GST validation (optional but if provided, must be valid)
    if (formData.gstNumber.trim() && !enhancedApiClient.validateGSTNumber(formData.gstNumber)) {
      newErrors.gstNumber = 'Please enter a valid GST number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleGSTVerification = async (gstNumber: string) => {
    if (!gstNumber.trim() || !enhancedApiClient.validateGSTNumber(gstNumber)) {
      setGstVerificationStatus('idle');
      return;
    }

    setGstVerificationStatus('verifying');
    
    try {
      const response = await enhancedApiClient.verifyGST(gstNumber);
      if (response.success) {
        setGstVerificationStatus('verified');
        // Update form data with verified GST details if available
        if (response.data) {
          setFormData(prev => ({
            ...prev,
            businessName: response.data.legalName || prev.businessName,
          }));
        }
      } else {
        setGstVerificationStatus('error');
      }
    } catch (error) {
      setGstVerificationStatus('error');
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await register({
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim(),
        password: formData.password,
        phone: formData.phone.trim() || undefined,
        businessName: formData.businessName.trim() || undefined,
        gstNumber: formData.gstNumber.trim() || undefined,
        agreedToEmailMarketing: formData.agreedToEmailMarketing,
        agreedToSmsMarketing: formData.agreedToSmsMarketing,
      });

      if (result.success) {
        Alert.alert(
          'Registration Successful',
          'Your account has been created successfully! Please check your email for verification instructions.',
          [
            {
              text: 'OK',
              onPress: () => {
                onSuccess?.();
              },
            },
          ]
        );
      } else {
        Alert.alert(
          'Registration Failed',
          result.error || 'Failed to create account. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field: keyof RegisterFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (typeof value === 'string' && errors[field as keyof RegisterFormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // Handle GST verification
    if (field === 'gstNumber' && typeof value === 'string') {
      handleGSTVerification(value);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Heading2 align="center" color="foreground">
          Create Account
        </Heading2>
        <Body1 align="center" color="mutedForeground" style={styles.subtitle}>
          Join Benzochem Industries and access our chemical products
        </Body1>
      </View>

      <Card variant="elevated" padding="lg" style={styles.card}>
        <Form>
          <FormSection title="Personal Information">
            {/* Name Fields */}
            <View style={styles.row}>
              <FormField style={styles.halfField}>
                <FormLabel required>First Name</FormLabel>
                <Input
                  value={formData.firstName}
                  onChangeText={(value) => updateFormData('firstName', value)}
                  placeholder="Enter your first name"
                  error={errors.firstName}
                  leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
                />
                <FormMessage error={errors.firstName} />
              </FormField>

              <FormField style={styles.halfField}>
                <FormLabel required>Last Name</FormLabel>
                <Input
                  value={formData.lastName}
                  onChangeText={(value) => updateFormData('lastName', value)}
                  placeholder="Enter your last name"
                  error={errors.lastName}
                  leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
                />
                <FormMessage error={errors.lastName} />
              </FormField>
            </View>

            {/* Email Field */}
            <FormField>
              <FormLabel required>Email Address</FormLabel>
              <Input
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                placeholder="Enter your email address"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                error={errors.email}
                leftIcon={<Mail size={18} color={theme.colors.mutedForeground} />}
              />
              <FormMessage error={errors.email} />
            </FormField>

            {/* Phone Field */}
            <FormField>
              <FormLabel>Phone Number</FormLabel>
              <Input
                value={formData.phone}
                onChangeText={(value) => updateFormData('phone', value)}
                placeholder="Enter your phone number (optional)"
                keyboardType="phone-pad"
                error={errors.phone}
                leftIcon={<Phone size={18} color={theme.colors.mutedForeground} />}
              />
              <FormMessage error={errors.phone} />
            </FormField>
          </FormSection>

          <FormSeparator />

          <FormSection title="Security">
            {/* Password Field */}
            <FormField>
              <FormLabel required>Password</FormLabel>
              <Input
                value={formData.password}
                onChangeText={(value) => updateFormData('password', value)}
                placeholder="Create a strong password"
                secureTextEntry={!showPassword}
                error={errors.password}
                leftIcon={<Lock size={18} color={theme.colors.mutedForeground} />}
                rightIcon={
                  <TouchableOpacity
                    onPress={() => setShowPassword(!showPassword)}
                    activeOpacity={0.7}
                  >
                    {showPassword ? (
                      <EyeOff size={18} color={theme.colors.mutedForeground} />
                    ) : (
                      <Eye size={18} color={theme.colors.mutedForeground} />
                    )}
                  </TouchableOpacity>
                }
              />
              <FormMessage error={errors.password} />
            </FormField>

            {/* Confirm Password Field */}
            <FormField>
              <FormLabel required>Confirm Password</FormLabel>
              <Input
                value={formData.confirmPassword}
                onChangeText={(value) => updateFormData('confirmPassword', value)}
                placeholder="Confirm your password"
                secureTextEntry={!showConfirmPassword}
                error={errors.confirmPassword}
                leftIcon={<Lock size={18} color={theme.colors.mutedForeground} />}
                rightIcon={
                  <TouchableOpacity
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                    activeOpacity={0.7}
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={18} color={theme.colors.mutedForeground} />
                    ) : (
                      <Eye size={18} color={theme.colors.mutedForeground} />
                    )}
                  </TouchableOpacity>
                }
              />
              <FormMessage error={errors.confirmPassword} />
            </FormField>
          </FormSection>

          <FormSeparator />

          <FormSection title="Business Information" description="Optional - Provide your business details for better service">
            {/* Business Name Field */}
            <FormField>
              <FormLabel>Business Name</FormLabel>
              <Input
                value={formData.businessName}
                onChangeText={(value) => updateFormData('businessName', value)}
                placeholder="Enter your business name (optional)"
                leftIcon={<Building size={18} color={theme.colors.mutedForeground} />}
              />
            </FormField>

            {/* GST Number Field */}
            <FormField>
              <FormLabel>GST Number</FormLabel>
              <Input
                value={formData.gstNumber}
                onChangeText={(value) => updateFormData('gstNumber', value)}
                placeholder="Enter your GST number (optional)"
                autoCapitalize="characters"
                error={errors.gstNumber}
                leftIcon={<FileText size={18} color={theme.colors.mutedForeground} />}
                rightIcon={
                  gstVerificationStatus === 'verifying' ? (
                    <Text style={{ color: theme.colors.mutedForeground, fontSize: 12 }}>
                      Verifying...
                    </Text>
                  ) : gstVerificationStatus === 'verified' ? (
                    <Text style={{ color: theme.colors.success, fontSize: 12 }}>
                      ✓ Verified
                    </Text>
                  ) : gstVerificationStatus === 'error' ? (
                    <Text style={{ color: theme.colors.destructive, fontSize: 12 }}>
                      ✗ Invalid
                    </Text>
                  ) : undefined
                }
              />
              <FormMessage error={errors.gstNumber} />
            </FormField>
          </FormSection>

          <FormActions align="center">
            <Button
              title={isSubmitting ? 'Creating Account...' : 'Create Account'}
              onPress={handleSubmit}
              loading={isSubmitting}
              disabled={isSubmitting}
              variant="primary"
              size="lg"
              fullWidth
            />
          </FormActions>
        </Form>
      </Card>

      {/* Login Link */}
      {onLogin && (
        <View style={styles.loginSection}>
          <Body1 align="center" color="mutedForeground">
            Already have an account?{' '}
            <TouchableOpacity onPress={onLogin} activeOpacity={0.7}>
              <Text style={[styles.linkText, { color: theme.colors.primary }]}>
                Sign In
              </Text>
            </TouchableOpacity>
          </Body1>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 32,
    marginTop: 32,
  },
  subtitle: {
    marginTop: 8,
  },
  card: {
    marginBottom: 24,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfField: {
    flex: 1,
  },
  linkText: {
    fontSize: 14,
    fontFamily: 'System',
    fontWeight: '500',
  },
  loginSection: {
    alignItems: 'center',
    paddingVertical: 16,
  },
});

export default EnhancedRegisterForm;
