import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../contexts/ThemeContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import { Product, Collection } from '../types';
import { AppNavigationProp } from '../types/navigation';
import ProductCard from '../components/ProductCard';
import EnhancedSearch from '../components/EnhancedSearch';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import { Typography, Heading3, Body1, Body2 } from '../components/ui/Typography';
import { Filter, Grid, List, SlidersHorizontal } from 'lucide-react-native';

interface FilterState {
  collection: string;
  featured: boolean;
  sortBy: 'name' | 'price' | 'newest';
  sortOrder: 'asc' | 'desc';
}

const EnhancedProductCatalogScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<AppNavigationProp>();
  
  const [products, setProducts] = useState<Product[]>([]);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    collection: '',
    featured: false,
    sortBy: 'name',
    sortOrder: 'asc',
  });

  // Load initial data
  useFocusEffect(
    useCallback(() => {
      loadInitialData();
    }, [])
  );

  // Apply filters and search when data changes
  useEffect(() => {
    applyFiltersAndSearch();
  }, [products, searchQuery, filters]);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadProducts(),
        loadCollections(),
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await enhancedApiClient.getProducts({
        limit: 100,
        offset: 0,
      });
      
      if (response.success && response.data) {
        const transformedProducts = Array.isArray(response.data) 
          ? response.data.map(enhancedApiClient.transformApiProductToProduct)
          : [];
        setProducts(transformedProducts);
      }
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const loadCollections = async () => {
    try {
      const response = await enhancedApiClient.getCollections({
        limit: 50,
        visible: true,
      });
      
      if (response.success && response.data) {
        setCollections(response.data);
      }
    } catch (error) {
      console.error('Error loading collections:', error);
    }
  };

  const applyFiltersAndSearch = () => {
    let filtered = [...products];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.tags?.some(tag => tag.toLowerCase().includes(query)) ||
        product.collections?.edges?.some(edge => 
          edge.node.title.toLowerCase().includes(query)
        )
      );
    }

    // Apply collection filter
    if (filters.collection) {
      filtered = filtered.filter(product =>
        product.collections?.edges?.some(edge => 
          edge.node.title === filters.collection
        )
      );
    }

    // Apply featured filter
    if (filters.featured) {
      // Assuming featured products have a specific tag or metafield
      filtered = filtered.filter(product =>
        product.tags?.includes('featured') ||
        product.metafields?.some(field => field.key === 'featured' && field.value === 'true')
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (filters.sortBy) {
        case 'name':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'price':
          const priceA = parseFloat(a.priceRange.minVariantPrice.amount);
          const priceB = parseFloat(b.priceRange.minVariantPrice.amount);
          comparison = priceA - priceB;
          break;
        case 'newest':
          // Assuming products have a creation date or ID that indicates newness
          comparison = a.id.localeCompare(b.id);
          break;
      }
      
      return filters.sortOrder === 'desc' ? -comparison : comparison;
    });

    setFilteredProducts(filtered);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadInitialData();
    setIsRefreshing(false);
  };

  const handleProductPress = (product: Product) => {
    navigation.navigate('ProductDetail', { productId: product.id });
  };

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      collection: '',
      featured: false,
      sortBy: 'name',
      sortOrder: 'asc',
    });
    setSearchQuery('');
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.collection) count++;
    if (filters.featured) count++;
    if (searchQuery.trim()) count++;
    return count;
  };

  const renderProduct = ({ item }: { item: Product }) => (
    <ProductCard
      product={item}
      variant={viewMode === 'grid' ? 'default' : 'compact'}
      onPress={() => handleProductPress(item)}
      showAddToQuotation={true}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <EnhancedSearch
        onSearch={handleSearch}
        placeholder="Search products..."
        showSuggestions={true}
        showHistory={true}
      />

      {/* Filter and View Controls */}
      <View style={styles.controls}>
        <View style={styles.filterControls}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              { 
                backgroundColor: showFilters ? theme.colors.primary : theme.colors.muted,
                borderColor: theme.colors.border,
              }
            ]}
            onPress={() => setShowFilters(!showFilters)}
            activeOpacity={0.7}
          >
            <SlidersHorizontal 
              size={16} 
              color={showFilters ? theme.colors.primaryForeground : theme.colors.mutedForeground} 
            />
            <Text style={[
              styles.filterButtonText,
              { 
                color: showFilters ? theme.colors.primaryForeground : theme.colors.mutedForeground 
              }
            ]}>
              Filters
            </Text>
            {getActiveFiltersCount() > 0 && (
              <Badge variant="destructive" size="sm">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </TouchableOpacity>

          {getActiveFiltersCount() > 0 && (
            <Button
              title="Clear"
              onPress={clearFilters}
              variant="outline"
              size="sm"
            />
          )}
        </View>

        <View style={styles.viewControls}>
          <TouchableOpacity
            style={[
              styles.viewButton,
              { 
                backgroundColor: viewMode === 'grid' ? theme.colors.primary : 'transparent',
                borderColor: theme.colors.border,
              }
            ]}
            onPress={() => setViewMode('grid')}
            activeOpacity={0.7}
          >
            <Grid 
              size={16} 
              color={viewMode === 'grid' ? theme.colors.primaryForeground : theme.colors.mutedForeground} 
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.viewButton,
              { 
                backgroundColor: viewMode === 'list' ? theme.colors.primary : 'transparent',
                borderColor: theme.colors.border,
              }
            ]}
            onPress={() => setViewMode('list')}
            activeOpacity={0.7}
          >
            <List 
              size={16} 
              color={viewMode === 'list' ? theme.colors.primaryForeground : theme.colors.mutedForeground} 
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter Panel */}
      {showFilters && (
        <Card variant="outlined" padding="md" style={styles.filterPanel}>
          <Heading3 color="foreground" style={styles.filterTitle}>
            Filter Products
          </Heading3>

          {/* Collection Filter */}
          <View style={styles.filterSection}>
            <Body1 color="foreground" weight="medium">Collection</Body1>
            <View style={styles.collectionFilters}>
              <TouchableOpacity
                style={[
                  styles.collectionChip,
                  {
                    backgroundColor: !filters.collection ? theme.colors.primary : theme.colors.muted,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => handleFilterChange('collection', '')}
                activeOpacity={0.7}
              >
                <Text style={[
                  styles.collectionChipText,
                  { 
                    color: !filters.collection ? theme.colors.primaryForeground : theme.colors.mutedForeground 
                  }
                ]}>
                  All
                </Text>
              </TouchableOpacity>

              {collections.slice(0, 5).map((collection) => (
                <TouchableOpacity
                  key={collection.id}
                  style={[
                    styles.collectionChip,
                    {
                      backgroundColor: filters.collection === collection.title 
                        ? theme.colors.primary 
                        : theme.colors.muted,
                      borderColor: theme.colors.border,
                    }
                  ]}
                  onPress={() => handleFilterChange('collection', collection.title)}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.collectionChipText,
                    { 
                      color: filters.collection === collection.title 
                        ? theme.colors.primaryForeground 
                        : theme.colors.mutedForeground 
                    }
                  ]}>
                    {collection.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Sort Options */}
          <View style={styles.filterSection}>
            <Body1 color="foreground" weight="medium">Sort By</Body1>
            <View style={styles.sortOptions}>
              {[
                { key: 'name', label: 'Name' },
                { key: 'price', label: 'Price' },
                { key: 'newest', label: 'Newest' },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.sortOption,
                    {
                      backgroundColor: filters.sortBy === option.key 
                        ? theme.colors.primary 
                        : theme.colors.muted,
                      borderColor: theme.colors.border,
                    }
                  ]}
                  onPress={() => handleFilterChange('sortBy', option.key)}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.sortOptionText,
                    { 
                      color: filters.sortBy === option.key 
                        ? theme.colors.primaryForeground 
                        : theme.colors.mutedForeground 
                    }
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </Card>
      )}

      {/* Results Summary */}
      <View style={styles.resultsHeader}>
        <Body2 color="mutedForeground">
          {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
          {searchQuery && ` for "${searchQuery}"`}
        </Body2>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.mutedForeground }]}>
          Loading products...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={filteredProducts}
        renderItem={renderProduct}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'System',
  },
  listContent: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  filterControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  filterButtonText: {
    fontSize: 14,
    fontFamily: 'System',
    fontWeight: '500',
  },
  viewControls: {
    flexDirection: 'row',
    gap: 4,
  },
  viewButton: {
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
  },
  filterPanel: {
    marginTop: 8,
    marginBottom: 16,
  },
  filterTitle: {
    marginBottom: 16,
  },
  filterSection: {
    marginBottom: 16,
  },
  collectionFilters: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  collectionChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  collectionChipText: {
    fontSize: 12,
    fontFamily: 'System',
    fontWeight: '500',
  },
  sortOptions: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  sortOption: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  sortOptionText: {
    fontSize: 14,
    fontFamily: 'System',
    fontWeight: '500',
  },
  resultsHeader: {
    marginBottom: 8,
  },
});

export default EnhancedProductCatalogScreen;
