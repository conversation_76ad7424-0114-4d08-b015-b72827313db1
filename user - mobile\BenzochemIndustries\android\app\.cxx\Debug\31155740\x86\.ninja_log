# ninja log v5
2	85	0	D:/edit-1/user - mobile/BenzochemIndustries/android/app/.cxx/Debug/31155740/x86/CMakeFiles/cmake.verify_globs	a8ffc77e64acbf8a
78828	102572	7746349749643271	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc23838159800ee9df8485485bb1aee4/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	38d5ad7af8cf6bff
60785	70793	7746918837071599	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7a394e0a5d06c49c0f1eae7d0b94ff65/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	9d545d877a232265
15039	29481	7746918422944255	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ComponentDescriptors.cpp.o	7fc15874af3f39f5
12868	21986	7746918348811309	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	5d291f0f0ad97219
10962	22399	7746918353090398	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	d97798f7141cab75
99570	114781	7746349871849765	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/35608b65eb206bd38ab8463f942d5332/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	9c53709365eefeb4
10434	24065	7746918368978855	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	dedb502f9f3021d1
12978	22829	7746918357047156	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	55bc4335fff7f2aa
45535	46614	7746918593434268	D:/edit-1/user - mobile/BenzochemIndustries/android/app/build/intermediates/cxx/Debug/31155740/obj/x86/libreact_codegen_safeareacontext.so	eb0bf2f6e9784d85
24091	38816	7746918517010839	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/42646916c7b9ea18c437c770f6a293c9/jni/react/renderer/components/safeareacontext/Props.cpp.o	f5e9e3450f6ed784
391	11268	7746918241830754	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	a399b420fb9b712e
11269	21011	7746918338636440	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	69f2a61e5fba2e9d
83	10962	7746918238699768	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/States.cpp.o	b30cdbc71a5a627
90774	111395	7746349837293743	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e788121a88a514ed7100636a7db2bfe5/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o	3d9092eab6512ea7
12594	23534	7746918364184912	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	c592938bbaca3958
289	14451	7746918273414625	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	f5b39347ba01a534
39856	70710	7746349430367609	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/efbb3617b5d19f1a08ecc22e5bb126b2/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	abcdf0d08d6aed07
96393	117834	7746349902300741	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/35608b65eb206bd38ab8463f942d5332/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	e3faef6986756d9d
49788	74621	7746918874822861	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ed9f0aa2a31eefd05bf62577036ca844/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	19eec43010309276
12623	24277	7746918371611213	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	9fb93f436c751bd2
203	15201	7746918280848512	CMakeFiles/appmodules.dir/OnLoad.cpp.o	ce65ba6e129947d9
13134	26468	7746918393583278	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	54084accf094ae1c
14452	29547	7746918424263695	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	310314cc48c166aa
118	12622	7746918255314299	RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/EventEmitters.cpp.o	71469c65b69aa92c
60809	85727	7746349580234290	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ffbe1b0934b8256d210967a6538e95b6/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	947563b213231e0b
13352	31232	7746918440786979	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	b9d8d7d2f703cd1b
51595	77088	7746349494640257	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a943a24a0b7931c80115a137fc61d956/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	88b247a6121983e5
15202	27542	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	dad985cac8b6dfc0
70711	90772	7746349631367730	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a943a24a0b7931c80115a137fc61d956/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	d64f55b04162813a
14620	27029	7746918399225789	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	b15682600e89c300
167	12867	7746918257626558	RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp.o	58fab8d692698d2e
21987	32376	7746918452515501	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp.o	82d6876c0c988ac8
22400	34236	7746918471453542	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/Props.cpp.o	7620112704f2a129
152	15038	7746918279171360	RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ComponentDescriptors.cpp.o	23e9b507feeaf669
21012	34454	7746918473596361	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/RNImagePickerSpec-generated.cpp.o	176d795529780f9f
133	14619	7746918274829897	RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp.o	f49acdaeabdf8537
68	12593	7746918254696365	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp.o	4faf24d23774a2ab
55	12977	7746918258005303	RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/RNPermissionsSpec-generated.cpp.o	3b85288faea8aa83
43	13351	7746918262552718	RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp.o	49c81302bd0445e3
77089	109150	7746349813371909	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c5b0df2b1702b96a628eeb9e3ad4cd28/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	b65c0eb0d2ce3e10
182	13133	7746918260350094	RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/Props.cpp.o	496cbdce234ca22f
104	10433	7746918233088904	RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/States.cpp.o	f45bccb4d54adfef
81005	123368	7746349957180585	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9d3c61a9d823f13e3ce2bdb3c1fe355a/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	e4839a3eeb6ea7c5
42689	59040	7746349314378457	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/026343b31e30f484cbad8991af434a9f/react/renderer/components/safeareacontext/States.cpp.o	fefa553c1cc78fa4
41955	60791	7746349331824374	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b4ed1ff22233f805701215bd12c0f1bb/renderer/components/safeareacontext/EventEmitters.cpp.o	ef90216679567a49
74503	99569	7746349719390023	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/23281d632c0bb145b60ece9770c14a49/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	e8f091abf2cca6d4
46066	65771	7746349381560533	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5b8bb3a966c419be4ab317a644451ed4/components/safeareacontext/RNCSafeAreaViewState.cpp.o	fc5d016df31f5320
41854	68362	7746349405925315	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51cc1b8c8c866c371e85db24ae5bb90e/jni/react/renderer/components/safeareacontext/Props.cpp.o	137ea004f1e96783
45662	68956	7746349413344643	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b4ed1ff22233f805701215bd12c0f1bb/renderer/components/safeareacontext/ShadowNodes.cpp.o	2a230ae72920b176
47460	71861	7746349442407593	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8da22fb47a7719e724f8084334f5b850/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	5d3443d420152309
54330	74493	7746349467603354	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a943a24a0b7931c80115a137fc61d956/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	257de230807717c2
47665	69149	7746918820067395	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/18288e7de35c4491d58470712aea3472/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	76456037bc9bea
41169	74798	7746349469203764	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/917a8e83c12ccd015a00cf4e541850db/components/safeareacontext/ComponentDescriptors.cpp.o	2a24a4a25febf573
50638	77647	7746349499623562	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ffbe1b0934b8256d210967a6538e95b6/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	e3c814fc22f24e82
99197	118154	7746349905643666	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/35608b65eb206bd38ab8463f942d5332/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	5da52a53ea071596
59211	78809	7746349511496427	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9e2cd8ee03e9d55bd6555aaa4e60b9de/safeareacontext/safeareacontextJSI-generated.cpp.o	e1e2a039ee35cfb9
54151	81568	7746349539472855	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/23281d632c0bb145b60ece9770c14a49/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	51559a173281207d
59045	80386	7746349527318336	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ffab63a9d8fb958f44efb39b848162a/source/codegen/jni/safeareacontext-generated.cpp.o	48ac26a726f201c4
65786	86813	7746349591633192	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ffbe1b0934b8256d210967a6538e95b6/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o	b16cf3c6b78a7844
68368	88395	7746349607831233	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d5d7eae3c65e25c2f870bb3ea84004d0/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	d88d6ff639b9e877
74815	94433	7746349668210426	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9d3c61a9d823f13e3ce2bdb3c1fe355a/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	19a3186821d9d5f4
24278	42249	7746918551424733	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72c4c6a8049a7a32fc6a8c40978520c4/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	2c5c7fcb9bdf7bb
68965	95276	7746349676515637	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a943a24a0b7931c80115a137fc61d956/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	c53297b526954086
81585	96376	7746349687620612	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c5b0df2b1702b96a628eeb9e3ad4cd28/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	1172170ad8c0efcd
40896	56992	7746918698834691	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	83b1864a090db5bd
218	55170	7746918675999419	CMakeFiles/appmodules.dir/D_/edit-1/user_-_mobile/BenzochemIndustries/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	383fe56418349788
85736	101540	7746349739388777	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/125fd54ca57f41097fcdb2c524f24365/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	245f90a6dea18455
59391	73192	7746918861110292	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o	43e2a02c0be2e58d
94436	113608	7746349859711733	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e788121a88a514ed7100636a7db2bfe5/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	9faa251aab5ef200
77653	114883	7746349871613202	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/fc23838159800ee9df8485485bb1aee4/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	57fc18f53e07eb73
29482	40895	7746918537745283	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6abcd94a9d3c523bd686d71d6f94ee91/safeareacontext/safeareacontextJSI-generated.cpp.o	6ec7f9eebf9872a0
95278	116683	7746349890824258	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e788121a88a514ed7100636a7db2bfe5/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	e1db376cd1218b91
88397	118958	7746349913469149	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/e788121a88a514ed7100636a7db2bfe5/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	3401f8fb9fbf83f7
86830	119050	7746349913933583	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/125fd54ca57f41097fcdb2c524f24365/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	ce061492ece6303c
100266	121088	7746349935035769	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/35608b65eb206bd38ab8463f942d5332/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	dad639c9e08adc4e
73728	73915	7746918868218461	D:/edit-1/user - mobile/BenzochemIndustries/android/app/build/intermediates/cxx/Debug/31155740/obj/x86/libreact_codegen_rnsvg.so	d5b3cae2479bfffd
74622	74820	7746918877113734	D:/edit-1/user - mobile/BenzochemIndustries/android/app/build/intermediates/cxx/Debug/31155740/obj/x86/libreact_codegen_rnscreens.so	97c6fb45b0777cae
74820	75165	7746918880428727	D:/edit-1/user - mobile/BenzochemIndustries/android/app/build/intermediates/cxx/Debug/31155740/obj/x86/libappmodules.so	65d2907f2dd54c46
0	26	0	clean	12954478bf16701d
142	4794	7746933988787137	build.ninja	45b092e2a456b9aa
23537	35073	7746918479482557	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0b04ee844217bfa8e68d79be4767e07c/renderer/components/safeareacontext/EventEmitters.cpp.o	961f51aa31154844
22830	36141	7746918490494762	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/23fbc28845a3db927d9ec2878b8a62af/components/safeareacontext/RNCSafeAreaViewState.cpp.o	7b2632e234b55eb7
27543	36852	7746918497533431	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3662988838beb7fa98a4c0c0a21333ab/react/renderer/components/safeareacontext/States.cpp.o	62ef383cd724d632
26469	41499	7746918543729591	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0b04ee844217bfa8e68d79be4767e07c/renderer/components/safeareacontext/ShadowNodes.cpp.o	f0bcf057584f5c0e
29548	42551	7746918554604118	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/38dd9e1ff3ef86ca506473957abddc61/source/codegen/jni/safeareacontext-generated.cpp.o	30e066d7292d2132
27030	45534	7746918583292180	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/120b203c5493bbf2c9feee648b3a56a5/components/safeareacontext/ComponentDescriptors.cpp.o	9e41a5966cd3c382
34237	46364	7746918592636942	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a25237f3710a96d259569f11f7245f1d/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o	4933f366a51e3a0f
32376	47526	7746918604295810	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/57a8e746ed99515e0033e6880a550173/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	771762f153386070
31233	47664	7746918605212750	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/70a669ece8dc6e7bd30b58bc4943fcee/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	e2aad4c7f382aee5
36853	49787	7746918626813599	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8dcac731058689eeacfa4fa890a26da1/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	559bfcb32b5bbfef
34455	50122	7746918630227190	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a25237f3710a96d259569f11f7245f1d/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	4e73875e2344431b
35074	50936	7746918638423060	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8dcac731058689eeacfa4fa890a26da1/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	899a175ab01dd74f
38817	51163	7746918640674839	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8dcac731058689eeacfa4fa890a26da1/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	ea3a1aeb21f0a86
36144	52732	7746918656018966	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a25237f3710a96d259569f11f7245f1d/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o	46d8c98196711a0e
46365	55943	7746918688417869	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ffedd07f508e646e18a7d160ac3794e/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	5aed627ae83e7103
41500	56038	7746918689296075	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/57a8e746ed99515e0033e6880a550173/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	c2a6ece2be4834d7
42250	56150	7746918690581748	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/048a6aa9ce59e9bd428a6eb8c1ee65d1/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	976310d59d459b41
46615	58612	7746918714987936	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ed9f0aa2a31eefd05bf62577036ca844/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	2512b7170d7bc5bc
42552	59390	7746918722122271	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8dcac731058689eeacfa4fa890a26da1/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o	4a7963cb8a325fe4
50937	60784	7746918736947504	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d2bdf217deaf224dbb1d4f54bb776dbd/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o	b43d0005b155d73
47527	62080	7746918749889455	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/18288e7de35c4491d58470712aea3472/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	ef98a4dcda391513
51164	64391	7746918772483911	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d2bdf217deaf224dbb1d4f54bb776dbd/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o	752d081137646f
56992	65921	7746918788235109	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7c25884837876db20ff0ddbaa4f047f7/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o	70f03496694d319a
56039	66835	7746918797203387	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7a394e0a5d06c49c0f1eae7d0b94ff65/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o	16484773124f61af
52733	67327	7746918801977397	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d2bdf217deaf224dbb1d4f54bb776dbd/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o	6d0e2deeac83a8cb
50123	68122	7746918809857574	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ffedd07f508e646e18a7d160ac3794e/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	ea13973e3e80f2f3
55944	69272	7746918821739969	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7a394e0a5d06c49c0f1eae7d0b94ff65/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o	dd631cf89234c43a
55171	70289	7746918831905943	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/d2bdf217deaf224dbb1d4f54bb776dbd/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o	d31c7632d2f63571
56151	72494	7746918853737717	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7c25884837876db20ff0ddbaa4f047f7/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o	4e7c9f6f16264371
58613	73727	7746918866300198	rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7a394e0a5d06c49c0f1eae7d0b94ff65/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o	2125637111440740
1	29	0	D:/edit-1/user - mobile/BenzochemIndustries/android/app/.cxx/Debug/31155740/x86/CMakeFiles/cmake.verify_globs	a8ffc77e64acbf8a
20	3820	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	dad985cac8b6dfc0
3821	4595	7746934041566736	D:/edit-1/user - mobile/BenzochemIndustries/android/app/build/intermediates/cxx/Debug/31155740/obj/x86/libappmodules.so	65d2907f2dd54c46
0	30	0	D:/edit-1/user - mobile/BenzochemIndustries/android/app/.cxx/Debug/31155740/x86/CMakeFiles/cmake.verify_globs	a8ffc77e64acbf8a
11	2107	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	dad985cac8b6dfc0
2107	2677	7747018049360020	D:/edit-1/user - mobile/BenzochemIndustries/android/app/build/intermediates/cxx/Debug/31155740/obj/x86/libappmodules.so	65d2907f2dd54c46
1	21	0	D:/edit-1/user - mobile/BenzochemIndustries/android/app/.cxx/Debug/31155740/x86/CMakeFiles/cmake.verify_globs	a8ffc77e64acbf8a
12	2130	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	dad985cac8b6dfc0
2131	2596	7747042925306739	D:/edit-1/user - mobile/BenzochemIndustries/android/app/build/intermediates/cxx/Debug/31155740/obj/x86/libappmodules.so	65d2907f2dd54c46
1	19	0	D:/edit-1/user - mobile/BenzochemIndustries/android/app/.cxx/Debug/31155740/x86/CMakeFiles/cmake.verify_globs	a8ffc77e64acbf8a
10	1890	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	dad985cac8b6dfc0
1890	2141	7747100013025404	D:/edit-1/user - mobile/BenzochemIndustries/android/app/build/intermediates/cxx/Debug/31155740/obj/x86/libappmodules.so	65d2907f2dd54c46
