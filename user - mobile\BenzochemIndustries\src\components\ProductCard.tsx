import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Animated,
} from 'react-native';
import {
  Package,
  Plus,
  Heart,
  Star,
  Zap,
  Shield,
  Award,
  ChevronRight,
  Bookmark,
  BookmarkCheck
} from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

import { useTheme } from '../contexts/ThemeContext';
import { useQuotation } from '../contexts/QuotationContext';
import { Product } from '../types';
import { AppNavigationProp } from '../types/navigation';

interface ProductCardProps {
  product: Product;
  variant?: 'default' | 'compact' | 'large' | 'featured' | 'list';
  showBadges?: boolean;
  showActions?: boolean;
}

const { width } = Dimensions.get('window');

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  variant = 'default',
  showBadges = true,
  showActions = true
}) => {
  const { theme } = useTheme();
  const { addItem } = useQuotation();
  const navigation = useNavigation<AppNavigationProp>();

  const [isFavorited, setIsFavorited] = useState(false);
  const [scaleAnim] = useState(new Animated.Value(1));

  const getImageUrl = () => {
    if (product.images?.edges?.length > 0) {
      return product.images.edges[0].node.url;
    }
    return null;
  };

  const getPrice = () => {
    if (!product.priceRange) return 'Request Quote';

    const amount = product.priceRange.minVariantPrice?.amount;
    const currency = product.priceRange.minVariantPrice?.currencyCode;

    if (amount && parseFloat(amount) > 0) {
      return `${currency === 'USD' ? '$' : '₹'}${parseFloat(amount).toLocaleString()}`;
    }
    return 'Request Quote';
  };

  const getMetafieldValue = (key: string) => {
    return product.metafields?.find(field => field.key === key)?.value;
  };

  const handlePress = () => {
    // Add press animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    navigation.navigate('ProductDetail', { productId: product.id });
  };

  const handleAddToQuotation = (e: any) => {
    e.stopPropagation();
    addItem({
      productId: product.id,
      productName: product.title,
      quantity: '1',
      unit: 'kg',
      specifications: '',
    });
  };

  const handleToggleFavorite = (e: any) => {
    e.stopPropagation();
    setIsFavorited(!isFavorited);
  };

  const imageUrl = getImageUrl();
  const price = getPrice();
  const purity = getMetafieldValue('purity');
  const casNumber = getMetafieldValue('cas_number');
  const molecularFormula = getMetafieldValue('molecular_formula');
  const grade = getMetafieldValue('grade');

  // Dynamic sizing based on variant
  const getCardDimensions = () => {
    switch (variant) {
      case 'compact':
        return { width: (width - 48) / 2, imageHeight: 140 };
      case 'large':
        return { width: width - 32, imageHeight: 220 };
      case 'featured':
        return { width: width - 32, imageHeight: 180 };
      case 'list':
        return { width: width - 32, imageHeight: 80 };
      default:
        return { width: width * 0.75, imageHeight: 160 };
    }
  };

  const { width: cardWidth, imageHeight } = getCardDimensions();

  // List variant has different layout
  if (variant === 'list') {
    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          style={[styles.listCard, { backgroundColor: theme.colors.card }]}
          onPress={handlePress}
          activeOpacity={0.8}
        >
          {/* Image */}
          <View style={[styles.listImageContainer, { height: imageHeight }]}>
            {imageUrl ? (
              <Image source={{ uri: imageUrl }} style={styles.listImage} resizeMode="cover" />
            ) : (
              <View style={[styles.listPlaceholder, { backgroundColor: theme.colors.muted }]}>
                <Package size={24} color={theme.colors.mutedForeground} />
              </View>
            )}
          </View>

          {/* Content */}
          <View style={styles.listContent}>
            <View style={styles.listHeader}>
              <Text style={[styles.listTitle, { color: theme.colors.foreground }]} numberOfLines={2}>
                {product.title}
              </Text>
              {showBadges && (
                <View style={styles.listBadges}>
                  <View style={[styles.gradeBadge, { backgroundColor: theme.colors.primary + '15' }]}>
                    <Text style={[styles.gradeBadgeText, { color: theme.colors.primary }]}>
                      {grade || 'Premium'}
                    </Text>
                  </View>
                </View>
              )}
            </View>

            <View style={styles.listProperties}>
              {purity && (
                <Text style={[styles.listPropertyText, { color: theme.colors.mutedForeground }]}>
                  Purity: {purity}
                </Text>
              )}
              {casNumber && (
                <Text style={[styles.listPropertyText, { color: theme.colors.mutedForeground }]}>
                  CAS: {casNumber}
                </Text>
              )}
            </View>

            <View style={styles.listFooter}>
              <Text style={[styles.listPrice, { color: theme.colors.primary }]}>
                {price}
              </Text>
              {showActions && (
                <TouchableOpacity
                  style={[styles.listQuoteButton, { backgroundColor: theme.colors.primary }]}
                  onPress={handleAddToQuotation}
                >
                  <Text style={[styles.listQuoteButtonText, { color: theme.colors.primaryForeground }]}>
                    Quote
                  </Text>
                  <Plus size={14} color={theme.colors.primaryForeground} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {showActions && (
            <TouchableOpacity
              style={[styles.listFavoriteButton, { backgroundColor: theme.colors.background + 'CC' }]}
              onPress={handleToggleFavorite}
            >
              {isFavorited ? (
                <BookmarkCheck size={16} color={theme.colors.primary} />
              ) : (
                <Bookmark size={16} color={theme.colors.mutedForeground} />
              )}
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Featured variant
  if (variant === 'featured') {
    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          style={[styles.featuredCard, { backgroundColor: theme.colors.card, width: cardWidth }]}
          onPress={handlePress}
          activeOpacity={0.8}
        >
          {/* Image with gradient overlay */}
          <View style={[styles.featuredImageContainer, { height: imageHeight }]}>
            {imageUrl ? (
              <Image source={{ uri: imageUrl }} style={styles.featuredImage} resizeMode="cover" />
            ) : (
              <View style={[styles.featuredPlaceholder, { backgroundColor: theme.colors.muted }]}>
                <Package size={40} color={theme.colors.mutedForeground} />
              </View>
            )}

            {/* Gradient overlay */}
            <View style={styles.featuredGradient} />

            {/* Top badges */}
            {showBadges && (
              <View style={styles.featuredTopBadges}>
                <View style={[styles.featuredBadge, { backgroundColor: theme.colors.warning }]}>
                  <Star size={12} color={theme.colors.warningForeground} />
                  <Text style={[styles.featuredBadgeText, { color: theme.colors.warningForeground }]}>
                    Featured
                  </Text>
                </View>
                {grade && (
                  <View style={[styles.featuredBadge, { backgroundColor: theme.colors.success }]}>
                    <Award size={12} color={theme.colors.successForeground} />
                    <Text style={[styles.featuredBadgeText, { color: theme.colors.successForeground }]}>
                      {grade}
                    </Text>
                  </View>
                )}
              </View>
            )}

            {/* Favorite button */}
            {showActions && (
              <TouchableOpacity
                style={[styles.featuredFavoriteButton, { backgroundColor: theme.colors.background + 'CC' }]}
                onPress={handleToggleFavorite}
              >
                <Heart
                  size={18}
                  color={isFavorited ? theme.colors.destructive : theme.colors.mutedForeground}
                  fill={isFavorited ? theme.colors.destructive : 'transparent'}
                />
              </TouchableOpacity>
            )}
          </View>

          {/* Content */}
          <View style={styles.featuredContent}>
            <Text style={[styles.featuredTitle, { color: theme.colors.foreground }]} numberOfLines={2}>
              {product.title}
            </Text>

            <View style={styles.featuredProperties}>
              {molecularFormula && (
                <View style={styles.featuredProperty}>
                  <Text style={[styles.featuredPropertyLabel, { color: theme.colors.mutedForeground }]}>
                    Formula:
                  </Text>
                  <Text style={[styles.featuredPropertyValue, { color: theme.colors.foreground }]}>
                    {molecularFormula}
                  </Text>
                </View>
              )}
              {purity && (
                <View style={styles.featuredProperty}>
                  <Text style={[styles.featuredPropertyLabel, { color: theme.colors.mutedForeground }]}>
                    Purity:
                  </Text>
                  <Text style={[styles.featuredPropertyValue, { color: theme.colors.foreground }]}>
                    {purity}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.featuredFooter}>
              <View style={styles.featuredPriceContainer}>
                <Text style={[styles.featuredPrice, { color: theme.colors.primary }]}>
                  {price}
                </Text>
                {price !== 'Request Quote' && (
                  <Text style={[styles.featuredPriceLabel, { color: theme.colors.mutedForeground }]}>
                    per kg
                  </Text>
                )}
              </View>

              {showActions && (
                <TouchableOpacity
                  style={[styles.featuredQuoteButton, { backgroundColor: theme.colors.primary }]}
                  onPress={handleAddToQuotation}
                >
                  <Text style={[styles.featuredQuoteButtonText, { color: theme.colors.primaryForeground }]}>
                    Get Quote
                  </Text>
                  <ChevronRight size={16} color={theme.colors.primaryForeground} />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Default/Compact/Large variants
  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        style={[
          styles.card,
          variant === 'compact' && styles.compactCard,
          variant === 'large' && styles.largeCard,
          {
            backgroundColor: theme.colors.card,
            width: cardWidth,
          }
        ]}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        {/* Product Image */}
        <View style={[styles.imageContainer, { height: imageHeight }]}>
          {imageUrl ? (
            <Image source={{ uri: imageUrl }} style={styles.image} resizeMode="cover" />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: theme.colors.muted }]}>
              <Package size={variant === 'compact' ? 28 : 36} color={theme.colors.mutedForeground} />
            </View>
          )}

          {/* Top badges */}
          {showBadges && (
            <View style={styles.topBadges}>
              <View style={[styles.newBadge, { backgroundColor: theme.colors.primary }]}>
                <Zap size={10} color={theme.colors.primaryForeground} />
                <Text style={[styles.newBadgeText, { color: theme.colors.primaryForeground }]}>
                  NEW
                </Text>
              </View>
              {grade && (
                <View style={[styles.gradeBadge, { backgroundColor: theme.colors.success + 'E6' }]}>
                  <Text style={[styles.gradeBadgeText, { color: theme.colors.success }]}>
                    {grade}
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* Favorite Button */}
          {showActions && (
            <TouchableOpacity
              style={[styles.favoriteButton, { backgroundColor: theme.colors.background + 'CC' }]}
              onPress={handleToggleFavorite}
            >
              <Heart
                size={16}
                color={isFavorited ? theme.colors.destructive : theme.colors.mutedForeground}
                fill={isFavorited ? theme.colors.destructive : 'transparent'}
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Product Info */}
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.foreground }]} numberOfLines={2}>
            {product.title}
          </Text>

          <Text style={[styles.category, { color: theme.colors.mutedForeground }]}>
            Chemical Product
          </Text>

          {/* Chemical Properties */}
          {(purity || casNumber) && variant !== 'compact' && (
            <View style={styles.properties}>
              {purity && (
                <View style={styles.propertyRow}>
                  <Shield size={12} color={theme.colors.mutedForeground} />
                  <Text style={[styles.propertyText, { color: theme.colors.mutedForeground }]}>
                    Purity: {purity}
                  </Text>
                </View>
              )}
              {casNumber && (
                <View style={styles.propertyRow}>
                  <Award size={12} color={theme.colors.mutedForeground} />
                  <Text style={[styles.propertyText, { color: theme.colors.mutedForeground }]}>
                    CAS: {casNumber}
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* Price and Actions */}
          <View style={styles.footer}>
            <View style={styles.priceContainer}>
              <Text style={[styles.price, { color: theme.colors.primary }]}>
                {price}
              </Text>
              {price !== 'Request Quote' && (
                <Text style={[styles.priceLabel, { color: theme.colors.mutedForeground }]}>
                  per kg
                </Text>
              )}
            </View>

            {showActions && (
              <TouchableOpacity
                style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleAddToQuotation}
              >
                <Plus size={16} color={theme.colors.primaryForeground} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Default/Compact/Large Card Styles
  card: {
    borderRadius: 20,
    overflow: 'hidden',
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
  },
  compactCard: {
    borderRadius: 16,
    elevation: 3,
    shadowOpacity: 0.08,
    shadowRadius: 6,
  },
  largeCard: {
    borderRadius: 24,
    elevation: 6,
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  imageContainer: {
    position: 'relative',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  topBadges: {
    position: 'absolute',
    top: 12,
    left: 12,
    flexDirection: 'row',
    gap: 6,
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backdropFilter: 'blur(10px)',
  },
  newBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  newBadgeText: {
    fontSize: 10,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  gradeBadge: {
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 8,
  },
  gradeBadgeText: {
    fontSize: 9,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 6,
    lineHeight: 22,
  },
  category: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 10,
    opacity: 0.8,
  },
  properties: {
    marginBottom: 14,
    gap: 4,
  },
  propertyRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  propertyText: {
    fontSize: 12,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  priceContainer: {
    flex: 1,
  },
  price: {
    fontSize: 18,
    fontWeight: '800',
    letterSpacing: -0.3,
  },
  priceLabel: {
    fontSize: 11,
    fontWeight: '500',
    marginTop: 2,
    opacity: 0.7,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },

  // List Card Styles
  listCard: {
    flexDirection: 'row',
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    position: 'relative',
  },
  listImageContainer: {
    width: 80,
    overflow: 'hidden',
  },
  listImage: {
    width: '100%',
    height: '100%',
  },
  listPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 20,
    flex: 1,
    marginRight: 8,
  },
  listBadges: {
    flexDirection: 'row',
    gap: 4,
  },
  listProperties: {
    marginBottom: 12,
    gap: 2,
  },
  listPropertyText: {
    fontSize: 12,
    fontWeight: '500',
  },
  listFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  listPrice: {
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: -0.2,
  },
  listQuoteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    gap: 4,
  },
  listQuoteButtonText: {
    fontSize: 13,
    fontWeight: '600',
  },
  listFavoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Featured Card Styles
  featuredCard: {
    borderRadius: 24,
    overflow: 'hidden',
    marginBottom: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
  },
  featuredImageContainer: {
    position: 'relative',
    overflow: 'hidden',
  },
  featuredImage: {
    width: '100%',
    height: '100%',
  },
  featuredPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuredGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
    background: 'linear-gradient(transparent, rgba(0,0,0,0.3))',
  },
  featuredTopBadges: {
    position: 'absolute',
    top: 16,
    left: 16,
    flexDirection: 'row',
    gap: 8,
  },
  featuredBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  featuredBadgeText: {
    fontSize: 11,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  featuredFavoriteButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuredContent: {
    padding: 20,
  },
  featuredTitle: {
    fontSize: 20,
    fontWeight: '800',
    marginBottom: 12,
    lineHeight: 26,
  },
  featuredProperties: {
    marginBottom: 16,
    gap: 8,
  },
  featuredProperty: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featuredPropertyLabel: {
    fontSize: 13,
    fontWeight: '500',
    minWidth: 60,
  },
  featuredPropertyValue: {
    fontSize: 13,
    fontWeight: '600',
    flex: 1,
  },
  featuredFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  featuredPriceContainer: {
    flex: 1,
  },
  featuredPrice: {
    fontSize: 22,
    fontWeight: '900',
    letterSpacing: -0.5,
  },
  featuredPriceLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
    opacity: 0.7,
  },
  featuredQuoteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 16,
    gap: 6,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  featuredQuoteButtonText: {
    fontSize: 14,
    fontWeight: '700',
  },
});

export default ProductCard;

export default ProductCard;