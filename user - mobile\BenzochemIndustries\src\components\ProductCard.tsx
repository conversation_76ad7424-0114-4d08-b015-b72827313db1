import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { Package, Plus, Heart } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

import { useTheme } from '../contexts/ThemeContext';
import { useQuotation } from '../contexts/QuotationContext';
import { Product } from '../types';
import { AppNavigationProp } from '../types/navigation';

interface ProductCardProps {
  product: Product;
  variant?: 'default' | 'compact' | 'large';
}

const { width } = Dimensions.get('window');

const ProductCard: React.FC<ProductCardProps> = ({ product, variant = 'default' }) => {
  const { theme } = useTheme();
  const { addItem } = useQuotation();
  const navigation = useNavigation<AppNavigationProp>();

  const getImageUrl = () => {
    if (product.images?.edges?.length > 0) {
      return product.images.edges[0].node.url;
    }
    return null;
  };

  const getPrice = () => {
    if (!product.priceRange) return 'Quote';
    
    const amount = product.priceRange.minVariantPrice?.amount;
    const currency = product.priceRange.minVariantPrice?.currencyCode;
    
    if (amount && parseFloat(amount) > 0) {
      return `${currency === 'USD' ? '$' : '₹'}${amount}`;
    }
    return 'Quote';
  };

  const getMetafieldValue = (key: string) => {
    return product.metafields?.find(field => field.key === key)?.value;
  };

  const handlePress = () => {
    navigation.navigate('ProductDetail', { productId: product.id });
  };

  const handleAddToQuotation = (e: any) => {
    e.stopPropagation();
    addItem({
      productId: product.id,
      productName: product.title,
      quantity: '1',
      unit: 'kg',
      specifications: '',
    });
  };

  const handleToggleFavorite = (e: any) => {
    e.stopPropagation();
    // Implement favorite functionality
  };

  const imageUrl = getImageUrl();
  const price = getPrice();
  const purity = getMetafieldValue('purity');
  const casNumber = getMetafieldValue('cas_number');

  const cardWidth = variant === 'compact' ? width * 0.45 : variant === 'large' ? width * 0.8 : width * 0.7;
  const imageHeight = variant === 'compact' ? 120 : variant === 'large' ? 200 : 160;

  return (
    <TouchableOpacity
      style={[
        styles.card, 
        { 
          backgroundColor: theme.colors.card,
          width: cardWidth,
        }
      ]}
      onPress={handlePress}
      activeOpacity={0.9}
    >
      {/* Product Image */}
      <View style={[styles.imageContainer, { height: imageHeight }]}>
        {imageUrl ? (
          <Image
            source={{ uri: imageUrl }}
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.placeholderImage, { backgroundColor: theme.colors.surface }]}>
            <Package size={variant === 'compact' ? 24 : 32} color={theme.colors.textSecondary} />
          </View>
        )}
        
        {/* Favorite Button */}
        <TouchableOpacity 
          style={[styles.favoriteButton, { backgroundColor: theme.colors.background + 'E6' }]}
          onPress={handleToggleFavorite}
        >
          <Heart size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>

        {/* New Badge */}
        <View style={[styles.newBadge, { backgroundColor: theme.colors.primary }]}>
          <Text style={[styles.newBadgeText, { color: theme.colors.background }]}>
            NEW
          </Text>
        </View>
      </View>

      {/* Product Info */}
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]} numberOfLines={2}>
          {product.title}
        </Text>

        <Text style={[styles.category, { color: theme.colors.textSecondary }]}>
          Chemical Product
        </Text>

        {/* Chemical Properties */}
        {(purity || casNumber) && variant !== 'compact' && (
          <View style={styles.properties}>
            {purity && (
              <Text style={[styles.propertyText, { color: theme.colors.textSecondary }]}>
                Purity: {purity}
              </Text>
            )}
            {casNumber && (
              <Text style={[styles.propertyText, { color: theme.colors.textSecondary }]}>
                CAS: {casNumber}
              </Text>
            )}
          </View>
        )}

        {/* Price and Actions */}
        <View style={styles.footer}>
          <View style={styles.priceContainer}>
            <Text style={[styles.price, { color: theme.colors.primary }]}>
              {price}
            </Text>
            {price !== 'Quote' && (
              <Text style={[styles.priceLabel, { color: theme.colors.textSecondary }]}>
                per kg
              </Text>
            )}
          </View>

          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleAddToQuotation}
          >
            <Plus size={16} color={theme.colors.background} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  newBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  newBadgeText: {
    fontSize: 10,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
    lineHeight: 20,
  },
  category: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  properties: {
    marginBottom: 12,
  },
  propertyText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    flex: 1,
  },
  price: {
    fontSize: 18,
    fontWeight: '800',
    letterSpacing: -0.3,
  },
  priceLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ProductCard;