import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import {
  ArrowLeft,
  Mail,
  Lock,
  Eye,
  EyeOff,
  CheckCircle,
  Shield,
  Sparkles,
  ChevronRight
} from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { Form, FormField, FormLabel, FormMessage, FormSection, FormActions } from '../components/ui/Form';
import Card from '../components/ui/Card';
import Badge from '../components/ui/Badge';
import { Heading1, Body1, Body2 } from '../components/ui/Typography';

const { width } = Dimensions.get('window');

const LoginScreen = () => {
  const { theme } = useTheme();
  const { login, isLoading } = useAuth();
  const navigation = useNavigation();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(30));

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim())) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 5) {
      newErrors.password = 'Password must be at least 5 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: 'email' | 'password', value: string) => {
    if (field === 'email') {
      setEmail(value);
    } else {
      setPassword(value);
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    const result = await login(email.trim(), password);

    if (result.success) {
      navigation.navigate('Main' as never);
    } else {
      Alert.alert('Login Failed', result.error || 'An error occurred');
    }
  };

  const goBack = () => {
    navigation.goBack();
  };



  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar
        barStyle={theme.dark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />

      {/* Background Elements */}
      <View style={styles.backgroundElements}>
        <View style={[styles.backgroundCircle, styles.circle1, { backgroundColor: theme.colors.primary + '10' }]} />
        <View style={[styles.backgroundCircle, styles.circle2, { backgroundColor: theme.colors.secondary + '15' }]} />
      </View>

      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity
                style={[styles.backButton, { backgroundColor: theme.colors.card }]}
                onPress={goBack}
                activeOpacity={0.7}
              >
                <ArrowLeft size={20} color={theme.colors.foreground} />
              </TouchableOpacity>
            </View>

            {/* Hero Section */}
            <Animated.View
              style={[
                styles.heroSection,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                }
              ]}
            >
              <View style={styles.titleContainer}>
                <Badge variant="secondary" size="md" style={styles.welcomeBadge}>
                  <Shield size={12} color={theme.colors.mutedForeground} />
                  <Body2 color="mutedForeground" weight="medium" style={styles.badgeText}>
                    Secure Login
                  </Body2>
                </Badge>

                <Heading1 color="foreground" align="center" style={styles.title}>
                  Welcome Back
                </Heading1>

                <Body1 color="mutedForeground" align="center" style={styles.subtitle}>
                  Sign in to access your chemical products and quotations
                </Body1>
              </View>
            </Animated.View>

            {/* Login Form */}
            <Animated.View
              style={[
                styles.formSection,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                }
              ]}
            >
              <Card variant="elevated" padding="lg" style={styles.formCard}>
                <Form>
                  <FormSection>
                    {/* Email Field */}
                    <FormField>
                      <FormLabel required>Email Address</FormLabel>
                      <Input
                        value={email}
                        onChangeText={(value) => handleInputChange('email', value)}
                        placeholder="Enter your email address"
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoCorrect={false}
                        error={errors.email}
                        leftIcon={<Mail size={18} color={theme.colors.mutedForeground} />}
                      />
                      <FormMessage error={errors.email} />
                    </FormField>

                    {/* Password Field */}
                    <FormField>
                      <FormLabel required>Password</FormLabel>
                      <Input
                        value={password}
                        onChangeText={(value) => handleInputChange('password', value)}
                        placeholder="Enter your password"
                        secureTextEntry={!showPassword}
                        autoCapitalize="none"
                        autoCorrect={false}
                        error={errors.password}
                        leftIcon={<Lock size={18} color={theme.colors.mutedForeground} />}
                        rightIcon={
                          <TouchableOpacity
                            onPress={() => setShowPassword(!showPassword)}
                            activeOpacity={0.7}
                          >
                            {showPassword ? (
                              <EyeOff size={18} color={theme.colors.mutedForeground} />
                            ) : (
                              <Eye size={18} color={theme.colors.mutedForeground} />
                            )}
                          </TouchableOpacity>
                        }
                      />
                      <FormMessage error={errors.password} />
                    </FormField>

                    {/* Forgot Password */}
                    <TouchableOpacity style={styles.forgotPassword} activeOpacity={0.7}>
                      <Body2 color="primary" weight="medium">
                        Forgot your password?
                      </Body2>
                    </TouchableOpacity>

                    <FormActions align="center">
                      <Button
                        title={isLoading ? "Signing In..." : "Sign In to Account"}
                        onPress={handleLogin}
                        loading={isLoading}
                        disabled={isLoading}
                        variant="primary"
                        size="lg"
                        fullWidth
                        rightIcon={<ChevronRight size={16} color={theme.colors.primaryForeground} />}
                        style={styles.loginButton}
                      />
                    </FormActions>
                  </FormSection>
                </Form>
              </Card>
            </Animated.View>

            {/* Register Link */}
            <Animated.View
              style={[
                styles.registerSection,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                }
              ]}
            >
              <View style={styles.registerContainer}>
                <Body1 color="mutedForeground" align="center">
                  Don't have an account?{' '}
                  <TouchableOpacity
                    onPress={() => navigation.navigate('Register' as never)}
                    activeOpacity={0.7}
                  >
                    <Text style={[styles.registerLink, { color: theme.colors.primary }]}>
                      Create Account
                    </Text>
                  </TouchableOpacity>
                </Body1>
              </View>
            </Animated.View>

            {/* Security Notice */}
            <Animated.View
              style={[
                styles.securitySection,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                }
              ]}
            >
              <Card variant="outlined" padding="md" style={styles.securityCard}>
                <View style={styles.securityContent}>
                  <View style={[styles.securityIcon, { backgroundColor: theme.colors.success + '15' }]}>
                    <CheckCircle size={16} color={theme.colors.success} />
                  </View>
                  <View style={styles.securityText}>
                    <Body2 color="foreground" weight="medium">Secure & Encrypted</Body2>
                    <Body2 color="mutedForeground" style={styles.securityDescription}>
                      Your data is protected with industry-standard encryption
                    </Body2>
                  </View>
                </View>
              </Card>
            </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  backgroundCircle: {
    position: 'absolute',
    borderRadius: 9999,
  },
  circle1: {
    width: 200,
    height: 200,
    top: '15%',
    right: '10%',
  },
  circle2: {
    width: 150,
    height: 150,
    bottom: '25%',
    left: '5%',
  },
  safeArea: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 16,
    paddingBottom: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  heroSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  titleContainer: {
    alignItems: 'center',
    gap: 16,
  },
  welcomeBadge: {
    marginBottom: 8,
  },
  badgeText: {
    marginLeft: 4,
  },
  title: {
    letterSpacing: -0.5,
  },
  subtitle: {
    lineHeight: 24,
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  formSection: {
    marginBottom: 32,
  },
  formCard: {
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  forgotPassword: {
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 8,
  },
  loginButton: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  registerSection: {
    marginBottom: 24,
  },
  registerContainer: {
    alignItems: 'center',
  },
  registerLink: {
    fontSize: 16,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  securitySection: {
    marginBottom: 20,
  },
  securityCard: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  securityContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  securityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  securityText: {
    flex: 1,
  },
  securityDescription: {
    fontSize: 12,
    marginTop: 2,
  },
});

export default LoginScreen;