import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  Dimensions,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { AppNavigationProp } from '../types/navigation';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import { Typography, Heading2, Heading3, Body1, Body2 } from '../components/ui/Typography';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Mail, 
  RefreshCw, 
  Shield,
  ArrowRight,
  Timer,
  User,
  Calendar,
  AlertCircle,
  CheckCircle2,
  Crown,
  Sparkles
} from 'lucide-react-native';

const { width: screenWidth } = Dimensions.get('window');

interface StatusConfig {
  title: string;
  subtitle: string;
  message: string;
  badgeVariant: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info';
  badgeText: string;
  estimatedTime?: string;
  actionButton?: {
    text: string;
    action: () => void;
    variant: 'primary' | 'secondary' | 'outline';
    icon?: React.ReactNode;
  };
}

const WaitingListScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<AppNavigationProp>();
  const { user, isLoading, refreshUserData, isRefreshing } = useAuth();
  
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [nextCheckIn, setNextCheckIn] = useState(30);
  const [pulseAnimation] = useState(new Animated.Value(1));

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
      setNextCheckIn(prev => {
        if (prev <= 1) {
          handleRefresh();
          return 30;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Pulse animation for status icon
  useEffect(() => {
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulse.start();
    return () => pulse.stop();
  }, []);

  // Redirect based on user status
  useFocusEffect(
    React.useCallback(() => {
      if (user && user.status === 'approved') {
        navigation.navigate('Home');
      }
      if (!isLoading && !user) {
        navigation.navigate('Login');
      }
    }, [user, navigation, isLoading])
  );

  const handleRefresh = async () => {
    await refreshUserData();
    setLastUpdated(new Date());
  };

  const formatTimeElapsed = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getStatusIcon = () => {
    const iconProps = { size: 48, style: { transform: [{ scale: pulseAnimation }] } };
    
    switch (user?.status) {
      case 'pending':
        return (
          <Animated.View>
            <Clock {...iconProps} color={theme.colors.warning} />
          </Animated.View>
        );
      case 'approved':
        return (
          <Animated.View>
            <CheckCircle2 {...iconProps} color={theme.colors.success} />
          </Animated.View>
        );
      case 'rejected':
        return (
          <Animated.View>
            <XCircle {...iconProps} color={theme.colors.destructive} />
          </Animated.View>
        );
      case 'suspended':
        return (
          <Animated.View>
            <AlertCircle {...iconProps} color={theme.colors.destructive} />
          </Animated.View>
        );
      default:
        return (
          <Animated.View>
            <Timer {...iconProps} color={theme.colors.mutedForeground} />
          </Animated.View>
        );
    }
  };

  const getStatusConfig = (): StatusConfig => {
    switch (user?.status) {
      case 'pending':
        return {
          title: 'Account Under Review',
          subtitle: 'Your application is being carefully evaluated',
          message: 'Thank you for your registration with Benzochem Industries. Our team is currently reviewing your application to ensure compliance with our quality standards. You will receive an email notification once the review process is complete.',
          badgeVariant: 'warning',
          badgeText: 'In Review',
          estimatedTime: '24-48 hours'
        };
      case 'approved':
        return {
          title: 'Welcome to Benzochem Industries!',
          subtitle: 'Your account has been approved',
          message: 'Congratulations! Your account has been successfully approved. You now have full access to our premium chemical products and services. Start exploring our extensive catalog and request quotations.',
          badgeVariant: 'success',
          badgeText: 'Approved',
          actionButton: {
            text: 'Access Your Dashboard',
            action: () => navigation.navigate('Home'),
            variant: 'primary',
            icon: <Crown size={16} color={theme.colors.primaryForeground} />
          }
        };
      case 'rejected':
        return {
          title: 'Application Not Approved',
          subtitle: 'Your application requires attention',
          message: 'Unfortunately, your application could not be approved at this time. This may be due to incomplete information or verification issues. Please contact our support team for assistance with resubmission.',
          badgeVariant: 'destructive',
          badgeText: 'Rejected',
          actionButton: {
            text: 'Contact Support Team',
            action: () => navigation.navigate('Contact'),
            variant: 'primary',
            icon: <Mail size={16} color={theme.colors.primaryForeground} />
          }
        };
      case 'suspended':
        return {
          title: 'Account Suspended',
          subtitle: 'Your account access has been temporarily suspended',
          message: 'Your account has been temporarily suspended. This may be due to policy violations or security concerns. Please contact our support team immediately to resolve this issue.',
          badgeVariant: 'destructive',
          badgeText: 'Suspended',
          actionButton: {
            text: 'Contact Support Team',
            action: () => navigation.navigate('Contact'),
            variant: 'primary',
            icon: <Mail size={16} color={theme.colors.primaryForeground} />
          }
        };
      default:
        return {
          title: 'Account Status Unknown',
          subtitle: 'Please refresh to check your status',
          message: 'We are unable to determine your account status at this time. Please refresh the page or contact support if this issue persists.',
          badgeVariant: 'outline',
          badgeText: 'Unknown'
        };
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.mutedForeground }]}>
          Loading your account status...
        </Text>
      </View>
    );
  }

  if (!user) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.destructive }]}>
          Unable to load user data
        </Text>
        <Button
          title="Go to Login"
          onPress={() => navigation.navigate('Login')}
          variant="primary"
          size="lg"
          style={styles.errorButton}
        />
      </View>
    );
  }

  const statusConfig = getStatusConfig();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Animated Background Elements */}
      <View style={styles.backgroundElements}>
        <View style={[styles.backgroundCircle, styles.circle1, { backgroundColor: theme.colors.primary + '10' }]} />
        <View style={[styles.backgroundCircle, styles.circle2, { backgroundColor: theme.colors.secondary + '15' }]} />
        <View style={[styles.backgroundCircle, styles.circle3, { backgroundColor: theme.colors.accent + '08' }]} />
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Main Status Card */}
        <Card variant="elevated" padding="none" style={styles.statusCard}>
          {/* Header */}
          <View style={styles.cardHeader}>
            <View style={styles.statusHeader}>
              <View style={styles.statusIconContainer}>
                {getStatusIcon()}
                <Badge variant={statusConfig.badgeVariant} size="sm" style={styles.statusBadge}>
                  {statusConfig.badgeText}
                </Badge>
              </View>
              
              <View style={styles.statusTitleContainer}>
                <Heading2 color="foreground" style={styles.statusTitle}>
                  {statusConfig.title}
                </Heading2>
                <Body2 color="mutedForeground">
                  {statusConfig.subtitle}
                </Body2>
              </View>
            </View>
            
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={handleRefresh}
              disabled={isRefreshing}
              activeOpacity={0.7}
            >
              {isRefreshing ? (
                <ActivityIndicator size="small" color={theme.colors.primary} />
              ) : (
                <RefreshCw size={20} color={theme.colors.primary} />
              )}
            </TouchableOpacity>
          </View>

          {/* User Info */}
          <View style={styles.userInfo}>
            <View style={styles.userInfoItem}>
              <User size={16} color={theme.colors.mutedForeground} />
              <Body2 color="mutedForeground">
                {user.firstName} {user.lastName}
              </Body2>
            </View>
            <View style={styles.userInfoItem}>
              <Mail size={16} color={theme.colors.mutedForeground} />
              <Body2 color="mutedForeground">
                {user.email}
              </Body2>
            </View>
          </View>

          {/* Time Info */}
          <View style={styles.timeInfo}>
            <View style={styles.timeInfoItem}>
              <Timer size={16} color={theme.colors.mutedForeground} />
              <View>
                <Body2 color="mutedForeground" style={styles.timeLabel}>Time Elapsed</Body2>
                <Body1 color="foreground" weight="medium">{formatTimeElapsed(timeElapsed)}</Body1>
              </View>
            </View>
            
            <View style={styles.timeInfoItem}>
              <Calendar size={16} color={theme.colors.mutedForeground} />
              <View>
                <Body2 color="mutedForeground" style={styles.timeLabel}>Last Updated</Body2>
                <Body1 color="foreground" weight="medium">{lastUpdated.toLocaleTimeString()}</Body1>
              </View>
            </View>
          </View>

          {/* Status Message */}
          <View style={[styles.statusMessage, { backgroundColor: theme.colors.muted + '30' }]}>
            <Body1 color="foreground" align="center" style={styles.messageText}>
              {statusConfig.message}
            </Body1>
            
            {statusConfig.estimatedTime && (
              <View style={styles.estimatedTime}>
                <Clock size={16} color={theme.colors.mutedForeground} />
                <Body2 color="mutedForeground">
                  Estimated review time: <Text style={{ fontWeight: '600' }}>{statusConfig.estimatedTime}</Text>
                </Body2>
              </View>
            )}
          </View>

          {/* Action Button */}
          {statusConfig.actionButton && (
            <View style={styles.actionSection}>
              <Button
                title={statusConfig.actionButton.text}
                onPress={statusConfig.actionButton.action}
                variant={statusConfig.actionButton.variant}
                size="lg"
                fullWidth
                leftIcon={statusConfig.actionButton.icon}
                rightIcon={<ArrowRight size={16} color={theme.colors.primaryForeground} />}
              />
            </View>
          )}

          {/* Security Notice for Pending */}
          {user.status === 'pending' && (
            <View style={styles.securityNotice}>
              <View style={[styles.securityBadge, { backgroundColor: theme.colors.primary + '15' }]}>
                <Shield size={16} color={theme.colors.primary} />
                <Body2 color="primary" weight="medium">
                  Your application is being processed securely
                </Body2>
              </View>
            </View>
          )}
        </Card>

        {/* Footer Info */}
        <View style={styles.footerInfo}>
          <Body2 color="mutedForeground" align="center" style={styles.footerText}>
            This page automatically refreshes every 30 seconds to check for status updates
          </Body2>
          <View style={styles.footerDetails}>
            <Body2 color="mutedForeground">Last updated: {lastUpdated.toLocaleTimeString()}</Body2>
            <Body2 color="mutedForeground">•</Body2>
            <Body2 color="mutedForeground">Next check in: {nextCheckIn}s</Body2>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'System',
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'System',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 24,
  },
  errorButton: {
    minWidth: 200,
  },
  backgroundElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  backgroundCircle: {
    position: 'absolute',
    borderRadius: 9999,
  },
  circle1: {
    width: 200,
    height: 200,
    top: '20%',
    left: '20%',
  },
  circle2: {
    width: 300,
    height: 300,
    bottom: '20%',
    right: '20%',
  },
  circle3: {
    width: 100,
    height: 100,
    top: '50%',
    left: '50%',
    marginLeft: -50,
    marginTop: -50,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
    paddingTop: 50,
  },
  statusCard: {
    marginBottom: 24,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    padding: 24,
    paddingBottom: 16,
  },
  statusHeader: {
    flex: 1,
    alignItems: 'center',
  },
  statusIconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  statusBadge: {
    marginTop: 8,
  },
  statusTitleContainer: {
    alignItems: 'center',
  },
  statusTitle: {
    textAlign: 'center',
    marginBottom: 4,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 8,
  },
  userInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  userInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  timeInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeLabel: {
    fontSize: 12,
  },
  statusMessage: {
    margin: 24,
    padding: 20,
    borderRadius: 12,
  },
  messageText: {
    lineHeight: 24,
  },
  estimatedTime: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    marginTop: 16,
  },
  actionSection: {
    padding: 24,
    paddingTop: 0,
  },
  securityNotice: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  securityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  footerInfo: {
    alignItems: 'center',
    gap: 8,
  },
  footerText: {
    fontSize: 12,
    lineHeight: 16,
  },
  footerDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});

export default WaitingListScreen;
