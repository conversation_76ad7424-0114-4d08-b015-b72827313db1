// Core types for the mobile application

// Re-export navigation types
export * from './navigation';

export interface Product {
  id: string;
  title: string;
  description: string;
  descriptionHtml: string;
  tags: string[];
  quantity?: number;
  collections: {
    edges: Array<{
      node: { title: string };
    }>;
  };
  images: {
    edges: Array<{
      node: { url: string };
    }>;
  };
  media: {
    edges: Array<{
      node: {
        id: string;
        image: { url: string };
      };
    }>;
  };
  priceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
    maxVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  compareAtPriceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
    maxVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  metafields: Array<{
    id: string;
    key: string;
    namespace: string;
    value: string;
    type: string;
  }>;
}

export interface UserData {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  name: string;
  phone?: string;
  businessName?: string;
  gstNumber?: string;
  isGstVerified?: boolean;
  status: "pending" | "approved" | "rejected" | "suspended";
  role: "user" | "admin" | "super_admin";
  // Business information
  legalNameOfBusiness?: string;
  tradeName?: string;
  dateOfRegistration?: string;
  constitutionOfBusiness?: string;
  taxpayerType?: string;
  principalPlaceOfBusiness?: string;
  natureOfCoreBusinessActivity?: string;
  gstStatus?: string;
  // Marketing consent
  agreedToEmailMarketing?: boolean;
  agreedToSmsMarketing?: boolean;
  // Timestamps
  createdAt?: number;
  updatedAt?: number;
  lastLoginAt?: number;
}

export interface Collection {
  id: string;
  title: string;
  description?: string;
  handle: string;
  image?: {
    url: string;
    altText?: string;
  };
  seoTitle?: string;
  seoDescription?: string;
  status: string;
  sortOrder?: number;
  isVisible: boolean;
  productCount?: number;
  createdAt: number;
  updatedAt: number;
}

export interface Quotation {
  _id: string;
  userId: string;
  userEmail: string;
  userName: string;
  userPhone?: string;
  businessName?: string;
  products: Array<{
    productId: string;
    productName: string;
    quantity: string;
    unit: string;
    specifications?: string;
  }>;
  additionalRequirements?: string;
  deliveryLocation?: string;
  urgency: 'standard' | 'urgent' | 'asap';
  status: 'pending' | 'processing' | 'quoted' | 'accepted' | 'rejected' | 'expired';
  adminResponse?: {
    quotedBy: string;
    quotedAt: number;
    totalAmount?: string;
    validUntil?: number;
    terms?: string;
    notes?: string;
  };
  createdAt: number;
  updatedAt: number;
}

export interface ApiProduct {
  id: string;
  title: string;
  description: string;
  tags: string[];
  quantity?: number;
  collections: string[];
  images: Array<{
    url: string;
    altText?: string;
  }>;
  priceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
    maxVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  // Chemical-specific fields
  purity?: string;
  packaging?: string;
  casNumber?: string;
  hsnNumber?: string;
  molecularFormula?: string;
  molecularWeight?: string;
  appearance?: string;
  solubility?: string;
  phValue?: string;
  chemicalName?: string;
  features?: string[];
  applications?: string[];
  applicationDetails?: string[];
  status: string;
  featured: boolean;
  totalInventory?: number;
  createdAt: number;
  updatedAt: number;
}

export interface QuotationItem {
  productId: string;
  productName: string;
  quantity: string;
  unit: string;
  specifications?: string;
}

export interface AuthContextType {
  user: UserData | null;
  isLoading: boolean;
  isRefreshing: boolean;
  lastUpdated: Date | null;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phone?: string;
    businessName?: string;
    gstNumber?: string;
    legalNameOfBusiness?: string;
    tradeName?: string;
    dateOfRegistration?: string;
    constitutionOfBusiness?: string;
    taxpayerType?: string;
    principalPlaceOfBusiness?: string;
    natureOfCoreBusinessActivity?: string;
    gstStatus?: string;
    agreedToEmailMarketing?: boolean;
    agreedToSmsMarketing?: boolean;
  }) => Promise<{ success: boolean; error?: string; message?: string; redirectTo?: string }>;
  logout: () => Promise<void>;
  verifyGST: (gstNumber: string) => Promise<{ success: boolean; error?: string; data?: any }>;
  refreshUserData: () => Promise<void>;
}

export interface QuotationContextType {
  items: QuotationItem[];
  addItem: (item: QuotationItem) => void;
  removeItem: (productId: string) => void;
  updateItem: (productId: string, updates: Partial<QuotationItem>) => void;
  clearItems: () => void;
  getItemCount: () => number;
  submitQuotation: (data: {
    additionalRequirements?: string;
    deliveryLocation?: string;
    urgency?: 'standard' | 'urgent' | 'asap';
  }) => Promise<{ success: boolean; error?: string }>;
}

export interface ColorPalette {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
}

export interface ThemeColors {
  // Primary colors
  primary: string;
  primaryForeground: string;
  primaryDark: string;
  secondary: string;
  secondaryForeground: string;

  // Background colors
  background: string;
  foreground: string;
  surface: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;

  // Text colors
  text: string;
  textSecondary: string;

  // Muted colors
  muted: string;
  mutedForeground: string;
  accent: string;
  accentForeground: string;

  // Border and input
  border: string;
  input: string;
  ring: string;

  // UI colors
  notification: string;

  // Status colors
  error: string;
  errorForeground: string;
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
  info: string;
  infoForeground: string;

  // Utility colors
  medium: string;
  destructive: string;
  destructiveForeground: string;

  // Brand colors
  vanilla: ColorPalette;
  teal: ColorPalette;
}

export interface ThemeFonts {
  regular: string;
  medium: string;
  semibold: string;
  bold: string;
}

export interface ThemeSpacing {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

export interface ThemeBorderRadius {
  sm: number;
  md: number;
  lg: number;
  xl: number;
  full: number;
}

export interface Theme {
  dark: boolean;
  colors: ThemeColors;
  fonts: ThemeFonts;
  spacing: ThemeSpacing;
  borderRadius: ThemeBorderRadius;
}

export interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  themeMode: 'light' | 'dark' | 'system';
}