import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import { AppNavigationProp } from '../types/navigation';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { Form, FormField, FormLabel, FormMessage, FormSection, FormActions, FormSeparator } from '../components/ui/Form';
import Card from '../components/ui/Card';
import { Typography, Heading2, Body1, Body2 } from '../components/ui/Typography';
import Badge from '../components/ui/Badge';
import {
  User,
  Mail,
  Lock,
  Phone,
  Building,
  FileText,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle,
  Shield,
  ArrowRight,
  ArrowLeft
} from 'lucide-react-native';

interface RegisterFormData {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  countryCode: string;

  // Business Information
  gstNumber: string;
  legalNameOfBusiness: string;
  tradeName: string;
  dateOfRegistration: string;
  constitutionOfBusiness: string;
  taxpayerType: string;
  principalPlaceOfBusiness: string;
  natureOfCoreBusinessActivity: string;

  // Marketing Consent
  agreedToEmailMarketing: boolean;
  agreedToSmsMarketing: boolean;
}

interface RegisterFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  phone?: string;
  gstNumber?: string;
  legalNameOfBusiness?: string;
  tradeName?: string;
  principalPlaceOfBusiness?: string;
  natureOfCoreBusinessActivity?: string;
}

type TabType = 'personal' | 'business';

const RegisterScreen: React.FC = () => {
  const navigation = useNavigation<AppNavigationProp>();
  const { theme } = useTheme();
  const { register, isLoading } = useAuth();

  const [currentTab, setCurrentTab] = useState<TabType>('personal');
  const [personalInfoCompleted, setPersonalInfoCompleted] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isVerifyingGST, setIsVerifyingGST] = useState(false);
  const [gstVerified, setGstVerified] = useState(false);
  const [gstVerificationMessage, setGstVerificationMessage] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState<RegisterFormData>({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    phone: '',
    countryCode: '+91',

    // Business Information
    gstNumber: '',
    legalNameOfBusiness: '',
    tradeName: '',
    dateOfRegistration: '',
    constitutionOfBusiness: '',
    taxpayerType: '',
    principalPlaceOfBusiness: '',
    natureOfCoreBusinessActivity: '',

    // Marketing Consent
    agreedToEmailMarketing: false,
    agreedToSmsMarketing: false,
  });

  const [fieldErrors, setFieldErrors] = useState<RegisterFormErrors>({});

  // Validation functions matching web project
  const validateEmail = (email: string) => {
    if (!email) return "Please enter your email address";
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return "Please enter a valid email address";
    return null;
  };

  const validatePassword = (password: string) => {
    if (!password) return "Please enter a password";
    if (password.length < 5) return "Password must be at least 5 characters long";
    return null;
  };

  const validatePhone = (phone: string, countryCode: string) => {
    if (!phone) return "Please enter your phone number";
    if (!countryCode) return "Please select a country code";
    const fullPhone = `${countryCode}${phone}`;
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    if (!phoneRegex.test(fullPhone)) {
      return "Please enter a valid phone number including country code";
    }
    return null;
  };

  const validateName = (name: string, field: "firstName" | "lastName") => {
    if (!name) return `Please enter your ${field === "firstName" ? "first" : "last"} name`;
    if (name.length < 2) return `${field === "firstName" ? "First" : "Last"} name must be at least 2 characters long`;
    return null;
  };

  const validatePersonalInfo = () => {
    const errors: RegisterFormErrors = {};
    errors.firstName = validateName(formData.firstName, "firstName") || undefined;
    errors.lastName = validateName(formData.lastName, "lastName") || undefined;
    errors.email = validateEmail(formData.email) || undefined;
    errors.password = validatePassword(formData.password) || undefined;
    errors.phone = validatePhone(formData.phone, formData.countryCode) || undefined;

    setFieldErrors(errors);
    return Object.values(errors).every((err) => err === null || err === undefined);
  };

  const validateBusinessInfo = () => {
    const errors: RegisterFormErrors = {};

    // GST Number is required
    if (!formData.gstNumber) {
      errors.gstNumber = "GST Number is required";
    } else if (formData.gstNumber.length !== 15) {
      errors.gstNumber = "GST Number must be 15 characters long";
    }

    // Business details are required
    if (!formData.legalNameOfBusiness) {
      errors.legalNameOfBusiness = "Legal Name of Business is required";
    }

    if (!formData.tradeName) {
      errors.tradeName = "Trade Name is required";
    }

    if (!formData.principalPlaceOfBusiness) {
      errors.principalPlaceOfBusiness = "Business Address is required";
    }

    setFieldErrors(prev => ({ ...prev, ...errors }));
    return Object.keys(errors).length === 0;
  };

  const validateAllFields = () => {
    const personalValid = validatePersonalInfo();
    const businessValid = validateBusinessInfo();
    return personalValid && businessValid;
  };

  // GST Verification function matching web project
  const handleVerifyGST = async () => {
    if (!formData.gstNumber) {
      setGstVerificationMessage("Please enter a GST number");
      return;
    }

    if (formData.gstNumber.length !== 15) {
      setGstVerificationMessage("GST number must be 15 characters long");
      return;
    }

    setIsVerifyingGST(true);
    setGstVerificationMessage("");
    setError(null);

    try {
      const response = await enhancedApiClient.verifyGST(formData.gstNumber);

      if (response.success && response.data) {
        setGstVerified(true);
        setGstVerificationMessage("✅ GST verified successfully! Business details have been auto-filled.");

        // Auto-fill business information from GST data
        setFormData(prev => ({
          ...prev,
          legalNameOfBusiness: response.data.legalName || response.data.businessName || prev.legalNameOfBusiness,
          tradeName: response.data.tradeName || response.data.businessName || prev.tradeName,
          dateOfRegistration: response.data.dateOfRegistration || prev.dateOfRegistration,
          constitutionOfBusiness: response.data.constitutionOfBusiness || prev.constitutionOfBusiness,
          taxpayerType: response.data.taxpayerType || prev.taxpayerType,
          principalPlaceOfBusiness: response.data.address || response.data.principalPlaceOfBusiness || prev.principalPlaceOfBusiness,
          natureOfCoreBusinessActivity: response.data.natureOfCoreBusinessActivity || prev.natureOfCoreBusinessActivity,
        }));

        // Clear any previous field errors for auto-filled fields
        setFieldErrors(prev => ({
          ...prev,
          gstNumber: undefined,
          legalNameOfBusiness: undefined,
          tradeName: undefined,
          principalPlaceOfBusiness: undefined,
        }));
      } else {
        setGstVerified(false);
        setGstVerificationMessage(response.error || "❌ GST verification failed. Please check the number and try again.");
      }
    } catch (error) {
      console.error("GST verification failed:", error);
      setGstVerified(false);
      setGstVerificationMessage("⚠️ Network error. Please check your connection and try again.");
    } finally {
      setIsVerifyingGST(false);
    }
  };

  // Continue to business info tab
  const handleContinueToBusinessInfo = () => {
    if (validatePersonalInfo()) {
      setPersonalInfoCompleted(true);
      setCurrentTab("business");
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof RegisterFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear field error when user starts typing
    if (typeof value === 'string' && fieldErrors[field as keyof RegisterFormErrors]) {
      setFieldErrors(prev => ({ ...prev, [field]: null }));
    }
    // Clear general error when user starts typing
    if (error) {
      setError(null);
    }
  };

  // Handle registration submission
  const handleSubmit = async () => {
    if (!validateAllFields()) {
      return;
    }

    setError(null);
    setSuccess(null);

    try {
      const result = await register({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        phone: `${formData.countryCode}${formData.phone}`,
        businessName: formData.tradeName,
        gstNumber: formData.gstNumber,
        // Business information
        legalNameOfBusiness: formData.legalNameOfBusiness,
        tradeName: formData.tradeName,
        dateOfRegistration: formData.dateOfRegistration,
        constitutionOfBusiness: formData.constitutionOfBusiness,
        taxpayerType: formData.taxpayerType,
        principalPlaceOfBusiness: formData.principalPlaceOfBusiness,
        natureOfCoreBusinessActivity: formData.natureOfCoreBusinessActivity,
        // Marketing consent
        agreedToEmailMarketing: formData.agreedToEmailMarketing,
        agreedToSmsMarketing: formData.agreedToSmsMarketing,
      });

      if (result.success) {
        setSuccess(result.message || "🎉 Account created successfully! Your account is pending approval.");

        // Clear form data
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          phone: "",
          countryCode: "+91",
          legalNameOfBusiness: "",
          tradeName: "",
          gstNumber: "",
          dateOfRegistration: "",
          constitutionOfBusiness: "",
          taxpayerType: "",
          principalPlaceOfBusiness: "",
          natureOfCoreBusinessActivity: "",
          agreedToEmailMarketing: false,
          agreedToSmsMarketing: false,
        });

        // Reset verification states
        setGstVerified(false);
        setGstVerificationMessage("");
        setPersonalInfoCompleted(false);
        setCurrentTab("personal");

        // Show success alert and redirect
        Alert.alert(
          'Registration Successful',
          result.message || 'Your account has been created successfully! Please check your email for verification instructions.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to waiting list or login screen
                navigation.navigate('Login');
              },
            },
          ]
        );
      } else {
        setError(result.error || "Registration failed. Please try again.");
      }
    } catch (error) {
      console.error("Registration error:", error);
      setError("An unexpected error occurred. Please try again.");
    }
  };

  // Render tab navigation
  const renderTabNavigation = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[
          styles.tab,
          {
            backgroundColor: currentTab === 'personal' ? theme.colors.primary : 'transparent',
            borderColor: theme.colors.border,
          }
        ]}
        onPress={() => setCurrentTab('personal')}
        activeOpacity={0.7}
      >
        <View style={styles.tabContent}>
          <User
            size={16}
            color={currentTab === 'personal' ? theme.colors.primaryForeground : theme.colors.mutedForeground}
          />
          <Text style={[
            styles.tabText,
            {
              color: currentTab === 'personal' ? theme.colors.primaryForeground : theme.colors.mutedForeground
            }
          ]}>
            Personal Info
          </Text>
          {personalInfoCompleted && (
            <CheckCircle
              size={14}
              color={currentTab === 'personal' ? theme.colors.primaryForeground : theme.colors.success}
            />
          )}
        </View>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.tab,
          {
            backgroundColor: currentTab === 'business' ? theme.colors.primary : 'transparent',
            borderColor: theme.colors.border,
            opacity: personalInfoCompleted ? 1 : 0.5,
          }
        ]}
        onPress={() => personalInfoCompleted && setCurrentTab('business')}
        disabled={!personalInfoCompleted}
        activeOpacity={0.7}
      >
        <View style={styles.tabContent}>
          <Building
            size={16}
            color={currentTab === 'business' ? theme.colors.primaryForeground : theme.colors.mutedForeground}
          />
          <Text style={[
            styles.tabText,
            {
              color: currentTab === 'business' ? theme.colors.primaryForeground : theme.colors.mutedForeground
            }
          ]}>
            Business Info
          </Text>
          {gstVerified && (
            <CheckCircle
              size={14}
              color={currentTab === 'business' ? theme.colors.primaryForeground : theme.colors.success}
            />
          )}
        </View>
      </TouchableOpacity>
    </View>
  );

  // Render personal information form
  const renderPersonalInfoForm = () => (
    <FormSection
      title="Personal Information"
      description="Enter your personal details to create your account"
    >
      {/* Name Fields */}
      <View style={styles.row}>
        <FormField style={styles.halfField}>
          <FormLabel required>First Name</FormLabel>
          <Input
            value={formData.firstName}
            onChangeText={(value) => handleInputChange('firstName', value)}
            placeholder="Enter your first name"
            error={fieldErrors.firstName}
            leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
          />
          <FormMessage error={fieldErrors.firstName} />
        </FormField>

        <FormField style={styles.halfField}>
          <FormLabel required>Last Name</FormLabel>
          <Input
            value={formData.lastName}
            onChangeText={(value) => handleInputChange('lastName', value)}
            placeholder="Enter your last name"
            error={fieldErrors.lastName}
            leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
          />
          <FormMessage error={fieldErrors.lastName} />
        </FormField>
      </View>

      {/* Email Field */}
      <FormField>
        <FormLabel required>Email Address</FormLabel>
        <Input
          value={formData.email}
          onChangeText={(value) => handleInputChange('email', value)}
          placeholder="Enter your email address"
          keyboardType="email-address"
          autoCapitalize="none"
          autoComplete="email"
          error={fieldErrors.email}
          leftIcon={<Mail size={18} color={theme.colors.mutedForeground} />}
        />
        <FormMessage error={fieldErrors.email} />
      </FormField>

      {/* Password Field */}
      <FormField>
        <FormLabel required>Password</FormLabel>
        <Input
          value={formData.password}
          onChangeText={(value) => handleInputChange('password', value)}
          placeholder="Create a strong password"
          secureTextEntry={!showPassword}
          error={fieldErrors.password}
          leftIcon={<Lock size={18} color={theme.colors.mutedForeground} />}
          rightIcon={
            <TouchableOpacity
              onPress={() => setShowPassword(!showPassword)}
              activeOpacity={0.7}
            >
              {showPassword ? (
                <EyeOff size={18} color={theme.colors.mutedForeground} />
              ) : (
                <Eye size={18} color={theme.colors.mutedForeground} />
              )}
            </TouchableOpacity>
          }
        />
        <FormMessage error={fieldErrors.password} />
      </FormField>

      {/* Phone Field */}
      <FormField>
        <FormLabel required>Phone Number</FormLabel>
        <View style={styles.phoneContainer}>
          <Input
            value={formData.countryCode}
            onChangeText={(value) => handleInputChange('countryCode', value)}
            placeholder="+91"
            containerStyle={styles.countryCodeContainer}
          />
          <Input
            value={formData.phone}
            onChangeText={(value) => handleInputChange('phone', value)}
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
            error={fieldErrors.phone}
            leftIcon={<Phone size={18} color={theme.colors.mutedForeground} />}
            containerStyle={styles.phoneNumberContainer}
          />
        </View>
        <FormMessage error={fieldErrors.phone} />
      </FormField>

      <FormActions align="right">
        <Button
          title="Continue to Business Info"
          onPress={handleContinueToBusinessInfo}
          variant="primary"
          size="lg"
          rightIcon={<ArrowRight size={16} color={theme.colors.primaryForeground} />}
        />
      </FormActions>
    </FormSection>
  );

  // Render business information form
  const renderBusinessInfoForm = () => (
    <FormSection
      title="Business Information"
      description="Provide your business details for account verification"
    >
      {/* GST Number Field with Verification */}
      <FormField>
        <FormLabel required>GST Number</FormLabel>
        <View style={styles.gstContainer}>
          <Input
            value={formData.gstNumber}
            onChangeText={(value) => handleInputChange('gstNumber', value.toUpperCase())}
            placeholder="Enter your 15-digit GST number"
            autoCapitalize="characters"
            maxLength={15}
            error={fieldErrors.gstNumber}
            leftIcon={<FileText size={18} color={theme.colors.mutedForeground} />}
            containerStyle={styles.gstInputContainer}
            disabled={gstVerified}
          />
          <Button
            title={isVerifyingGST ? "Verifying..." : gstVerified ? "Verified" : "Verify"}
            onPress={handleVerifyGST}
            disabled={!formData.gstNumber || isVerifyingGST || gstVerified}
            variant={gstVerified ? "secondary" : "outline"}
            size="md"
            loading={isVerifyingGST}
            leftIcon={
              gstVerified ? (
                <CheckCircle size={14} color={theme.colors.success} />
              ) : (
                <Shield size={14} color={theme.colors.primary} />
              )
            }
            style={styles.verifyButton}
          />
        </View>
        <FormMessage error={fieldErrors.gstNumber} />
        {gstVerificationMessage && (
          <View style={[
            styles.verificationMessageContainer,
            {
              backgroundColor: gstVerified ? theme.colors.success + '15' : theme.colors.destructive + '15',
              borderColor: gstVerified ? theme.colors.success : theme.colors.destructive,
            }
          ]}>
            <Text style={[
              styles.verificationMessage,
              {
                color: gstVerified ? theme.colors.success : theme.colors.destructive
              }
            ]}>
              {gstVerificationMessage}
            </Text>
          </View>
        )}

        {!gstVerified && !isVerifyingGST && formData.gstNumber.length === 0 && (
          <Text style={[styles.gstHelpText, { color: theme.colors.mutedForeground }]}>
            💡 Enter your 15-digit GST number to auto-fill business details
          </Text>
        )}
      </FormField>

      {/* Legal Name of Business */}
      <FormField>
        <FormLabel required>Legal Name of Business</FormLabel>
        <Input
          value={formData.legalNameOfBusiness}
          onChangeText={(value) => handleInputChange('legalNameOfBusiness', value)}
          placeholder="As per GST registration"
          error={fieldErrors.legalNameOfBusiness}
          disabled={gstVerified}
        />
        <FormMessage error={fieldErrors.legalNameOfBusiness} />
      </FormField>

      {/* Trade Name */}
      <FormField>
        <FormLabel required>Trade Name</FormLabel>
        <Input
          value={formData.tradeName}
          onChangeText={(value) => handleInputChange('tradeName', value)}
          placeholder="Business trading name"
          error={fieldErrors.tradeName}
          disabled={gstVerified}

        />
        <FormMessage error={fieldErrors.tradeName} />
      </FormField>

      {/* Date of Registration */}
      <FormField>
        <FormLabel>Date of Registration</FormLabel>
        <Input
          value={formData.dateOfRegistration}
          onChangeText={(value) => handleInputChange('dateOfRegistration', value)}
          placeholder="DD/MM/YYYY"
          disabled={gstVerified}

        />
      </FormField>

      {/* Constitution of Business */}
      <FormField>
        <FormLabel>Constitution of Business</FormLabel>
        <Input
          value={formData.constitutionOfBusiness}
          onChangeText={(value) => handleInputChange('constitutionOfBusiness', value)}
          placeholder="e.g., Proprietorship, Partnership, Pvt Ltd"
          disabled={gstVerified}

        />
      </FormField>

      {/* Taxpayer Type */}
      <FormField>
        <FormLabel>Taxpayer Type</FormLabel>
        <Input
          value={formData.taxpayerType}
          onChangeText={(value) => handleInputChange('taxpayerType', value)}
          placeholder="Type of taxpayer"
          disabled={gstVerified}

        />
      </FormField>

      {/* Business Address */}
      <FormField>
        <FormLabel required>Business Address</FormLabel>
        <Input
          value={formData.principalPlaceOfBusiness}
          onChangeText={(value) => handleInputChange('principalPlaceOfBusiness', value)}
          placeholder="Enter the complete business address"
          multiline
          numberOfLines={3}
          error={fieldErrors.principalPlaceOfBusiness}
          disabled={gstVerified}

        />
        <FormMessage error={fieldErrors.principalPlaceOfBusiness} />
      </FormField>

      {/* Nature of Core Business Activity */}
      <FormField>
        <FormLabel>Nature of Core Business Activity</FormLabel>
        <Input
          value={formData.natureOfCoreBusinessActivity}
          onChangeText={(value) => handleInputChange('natureOfCoreBusinessActivity', value)}
          placeholder="Describe your main business activity"
          multiline
          numberOfLines={2}
          disabled={gstVerified}

        />
      </FormField>

      <FormActions align="space-between">
        <Button
          title="Back to Personal"
          onPress={() => setCurrentTab('personal')}
          variant="outline"
          size="lg"
          leftIcon={<ArrowLeft size={16} color={theme.colors.primary} />}
          style={styles.backButton}
        />

        <Button
          title={isLoading ? "Creating Account..." : "Create Account"}
          onPress={handleSubmit}
          loading={isLoading}
          disabled={isLoading}
          variant="primary"
          size="lg"
          style={styles.submitButton}
        />
      </FormActions>
    </FormSection>
  );

  // Show success message if registration completed
  if (success) {
    return (
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Card variant="elevated" padding="lg" style={styles.successCard}>
            <View style={styles.successContent}>
              <CheckCircle size={64} color={theme.colors.success} />
              <Heading2 align="center" color="foreground" style={styles.successTitle}>
                Registration Successful!
              </Heading2>
              <Body1 align="center" color="mutedForeground" style={styles.successMessage}>
                {success}
              </Body1>
              <Button
                title="Go to Login"
                onPress={() => navigation.navigate('Login')}
                variant="primary"
                size="lg"
                style={styles.successButton}
              />
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Heading2 align="center" color="foreground">
            Create Account
          </Heading2>
          <Body1 align="center" color="mutedForeground" style={styles.subtitle}>
            Join Benzochem Industries and access our chemical products
          </Body1>
        </View>

        {/* Tab Navigation */}
        {renderTabNavigation()}

        {/* Error Message */}
        {error && (
          <Card variant="outlined" padding="md" style={styles.errorCard}>
            <View style={styles.errorContent}>
              <AlertCircle size={20} color={theme.colors.destructive} />
              <Text style={[styles.errorText, { color: theme.colors.destructive }]}>
                {error}
              </Text>
            </View>
          </Card>
        )}

        {/* Form Card */}
        <Card variant="elevated" padding="lg" style={styles.formCard}>
          <Form>
            {currentTab === 'personal' ? renderPersonalInfoForm() : renderBusinessInfoForm()}
          </Form>
        </Card>

        {/* Login Link */}
        <View style={styles.loginSection}>
          <Body1 align="center" color="mutedForeground">
            Already have an account?{' '}
            <TouchableOpacity onPress={() => navigation.navigate('Login')} activeOpacity={0.7}>
              <Text style={[styles.linkText, { color: theme.colors.primary }]}>
                Sign In
              </Text>
            </TouchableOpacity>
          </Body1>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
    marginTop: 16,
  },
  subtitle: {
    marginTop: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    gap: 8,
  },
  tab: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'System',
    fontWeight: '600',
  },
  formCard: {
    marginBottom: 24,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfField: {
    flex: 1,
  },
  phoneContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  countryCodeContainer: {
    width: 80,
  },
  countryCodeInput: {
    textAlign: 'center',
  },
  phoneNumberContainer: {
    flex: 1,
  },
  gstContainer: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'flex-start',
  },
  gstInputContainer: {
    flex: 1,
  },
  verifyButton: {
    marginTop: 0,
    minWidth: 100,
  },
  verificationMessage: {
    fontSize: 12,
    fontFamily: 'System',
    marginTop: 4,
  },
  verificationMessageContainer: {
    marginTop: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  gstHelpText: {
    fontSize: 12,
    fontFamily: 'System',
    marginTop: 6,
    fontStyle: 'italic',
  },
  disabledInput: {
    opacity: 0.6,
  },
  backButton: {
    flex: 1,
    marginRight: 8,
  },
  submitButton: {
    flex: 2,
  },
  errorCard: {
    marginBottom: 16,
  },
  errorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  errorText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'System',
  },
  successCard: {
    marginTop: 50,
  },
  successContent: {
    alignItems: 'center',
    gap: 16,
  },
  successTitle: {
    marginTop: 16,
  },
  successMessage: {
    textAlign: 'center',
    marginBottom: 8,
  },
  successButton: {
    marginTop: 16,
    minWidth: 200,
  },
  loginSection: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  linkText: {
    fontSize: 14,
    fontFamily: 'System',
    fontWeight: '500',
  },
});

export default RegisterScreen;