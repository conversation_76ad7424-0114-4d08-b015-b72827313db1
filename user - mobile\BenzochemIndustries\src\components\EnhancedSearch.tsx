import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import Input from './ui/Input';
import Card from './ui/Card';
import { Search, Clock, TrendingUp, X } from 'lucide-react-native';

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'suggestion' | 'history' | 'trending';
}

interface EnhancedSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  showSuggestions?: boolean;
  showHistory?: boolean;
  autoFocus?: boolean;
}

const EnhancedSearch: React.FC<EnhancedSearchProps> = ({
  onSearch,
  placeholder = 'Search products...',
  showSuggestions = true,
  showHistory = true,
  autoFocus = false,
}) => {
  const { theme } = useTheme();
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [searchHistory, setSearchHistory] = useState<SearchSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // Load search history on component mount
  useEffect(() => {
    if (showHistory) {
      loadSearchHistory();
    }
  }, [showHistory]);

  // Debounced search suggestions
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (query.trim().length >= 2 && showSuggestions) {
      searchTimeoutRef.current = setTimeout(() => {
        fetchSuggestions(query.trim());
      }, 300);
    } else {
      setSuggestions([]);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query, showSuggestions]);

  const loadSearchHistory = async () => {
    try {
      const response = await enhancedApiClient.getSearchHistory();
      if (response.success && response.data) {
        const historyItems: SearchSuggestion[] = response.data.map((item: any, index: number) => ({
          id: `history-${index}`,
          text: item.query || item,
          type: 'history' as const,
        }));
        setSearchHistory(historyItems);
      }
    } catch (error) {
      console.error('Error loading search history:', error);
    }
  };

  const fetchSuggestions = async (searchQuery: string) => {
    setIsLoading(true);
    try {
      const response = await enhancedApiClient.getSearchSuggestions(searchQuery);
      if (response.success && response.data) {
        const suggestionItems: SearchSuggestion[] = response.data.map((item: any, index: number) => ({
          id: `suggestion-${index}`,
          text: typeof item === 'string' ? item : item.text || item.title,
          type: 'suggestion' as const,
        }));
        setSuggestions(suggestionItems);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async (searchQuery: string) => {
    const trimmedQuery = searchQuery.trim();
    if (!trimmedQuery) return;

    // Add to search history
    try {
      await enhancedApiClient.addToSearchHistory(trimmedQuery);
      // Reload search history to include the new search
      if (showHistory) {
        loadSearchHistory();
      }
    } catch (error) {
      console.error('Error adding to search history:', error);
    }

    // Perform search
    onSearch(trimmedQuery);
    setShowDropdown(false);
  };

  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    handleSearch(suggestion.text);
  };

  const clearQuery = () => {
    setQuery('');
    setSuggestions([]);
    setShowDropdown(false);
  };

  const renderSuggestionItem = ({ item }: { item: SearchSuggestion }) => {
    const getIcon = () => {
      switch (item.type) {
        case 'history':
          return <Clock size={16} color={theme.colors.mutedForeground} />;
        case 'trending':
          return <TrendingUp size={16} color={theme.colors.mutedForeground} />;
        default:
          return <Search size={16} color={theme.colors.mutedForeground} />;
      }
    };

    return (
      <TouchableOpacity
        style={styles.suggestionItem}
        onPress={() => handleSuggestionPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.suggestionIcon}>
          {getIcon()}
        </View>
        <Text style={[styles.suggestionText, { color: theme.colors.foreground }]}>
          {item.text}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderDropdown = () => {
    if (!showDropdown) return null;

    const allItems = [
      ...suggestions,
      ...(query.length < 2 && showHistory ? searchHistory.slice(0, 5) : []),
    ];

    if (allItems.length === 0 && !isLoading) {
      return null;
    }

    return (
      <Card variant="elevated" padding="none" style={styles.dropdown}>
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={theme.colors.primary} />
            <Text style={[styles.loadingText, { color: theme.colors.mutedForeground }]}>
              Loading suggestions...
            </Text>
          </View>
        )}
        
        {allItems.length > 0 && (
          <FlatList
            data={allItems}
            renderItem={renderSuggestionItem}
            keyExtractor={(item) => item.id}
            style={styles.suggestionsList}
            keyboardShouldPersistTaps="handled"
          />
        )}
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <Input
        value={query}
        onChangeText={setQuery}
        placeholder={placeholder}
        leftIcon={<Search size={20} color={theme.colors.mutedForeground} />}
        rightIcon={
          query.length > 0 ? (
            <TouchableOpacity onPress={clearQuery} activeOpacity={0.7}>
              <X size={20} color={theme.colors.mutedForeground} />
            </TouchableOpacity>
          ) : undefined
        }
        onFocus={() => setShowDropdown(true)}
        onBlur={() => {
          // Delay hiding dropdown to allow suggestion selection
          setTimeout(() => setShowDropdown(false), 150);
        }}
        onSubmitEditing={() => handleSearch(query)}
        returnKeyType="search"
        autoFocus={autoFocus}
        style={styles.searchInput}
      />
      
      {renderDropdown()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  searchInput: {
    marginBottom: 0,
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    maxHeight: 300,
    zIndex: 1001,
    marginTop: 4,
  },
  suggestionsList: {
    maxHeight: 300,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  suggestionIcon: {
    marginRight: 12,
  },
  suggestionText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'System',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'System',
  },
});

export default EnhancedSearch;
