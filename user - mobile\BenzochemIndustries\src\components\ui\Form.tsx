import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

// Form Container Component
export interface FormProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const Form: React.FC<FormProps> = ({ children, style }) => {
  return (
    <View style={[styles.form, style]}>
      {children}
    </View>
  );
};

// Form Field Component
export interface FormFieldProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const FormField: React.FC<FormFieldProps> = ({ children, style }) => {
  return (
    <View style={[styles.field, style]}>
      {children}
    </View>
  );
};

// Form Label Component
export interface FormLabelProps {
  children: React.ReactNode;
  required?: boolean;
  style?: TextStyle;
}

export const FormLabel: React.FC<FormLabelProps> = ({ 
  children, 
  required = false, 
  style 
}) => {
  const { theme } = useTheme();

  const getLabelStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.medium,
      fontSize: 14,
      color: theme.colors.foreground,
      marginBottom: 6,
    };
  };

  return (
    <Text style={[getLabelStyle(), style]}>
      {children}
      {required && (
        <Text style={{ color: theme.colors.destructive }}> *</Text>
      )}
    </Text>
  );
};

// Form Error Message Component
export interface FormMessageProps {
  children?: React.ReactNode;
  error?: string;
  style?: TextStyle;
}

export const FormMessage: React.FC<FormMessageProps> = ({ 
  children, 
  error, 
  style 
}) => {
  const { theme } = useTheme();

  const getMessageStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.destructive,
      marginTop: 4,
    };
  };

  const message = error || children;

  if (!message) {
    return null;
  }

  return (
    <Text style={[getMessageStyle(), style]}>
      {message}
    </Text>
  );
};

// Form Description Component
export interface FormDescriptionProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export const FormDescription: React.FC<FormDescriptionProps> = ({ 
  children, 
  style 
}) => {
  const { theme } = useTheme();

  const getDescriptionStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.mutedForeground,
      marginTop: 4,
    };
  };

  return (
    <Text style={[getDescriptionStyle(), style]}>
      {children}
    </Text>
  );
};

// Form Section Component
export interface FormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  style?: ViewStyle;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  style,
}) => {
  const { theme } = useTheme();

  const getTitleStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.semibold,
      fontSize: 18,
      color: theme.colors.foreground,
      marginBottom: 4,
    };
  };

  const getDescriptionStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.regular,
      fontSize: 14,
      color: theme.colors.mutedForeground,
      marginBottom: 16,
    };
  };

  return (
    <View style={[styles.section, style]}>
      {title && (
        <Text style={getTitleStyle()}>
          {title}
        </Text>
      )}
      {description && (
        <Text style={getDescriptionStyle()}>
          {description}
        </Text>
      )}
      {children}
    </View>
  );
};

// Form Actions Component
export interface FormActionsProps {
  children: React.ReactNode;
  style?: ViewStyle;
  align?: 'left' | 'center' | 'right' | 'space-between';
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  style,
  align = 'right',
}) => {
  const getActionsStyle = (): ViewStyle => {
    const alignmentStyles = {
      left: { justifyContent: 'flex-start' as const },
      center: { justifyContent: 'center' as const },
      right: { justifyContent: 'flex-end' as const },
      'space-between': { justifyContent: 'space-between' as const },
    };

    return {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
      marginTop: 24,
      ...alignmentStyles[align],
    };
  };

  return (
    <View style={[getActionsStyle(), style]}>
      {children}
    </View>
  );
};

// Form Separator Component
export interface FormSeparatorProps {
  style?: ViewStyle;
}

export const FormSeparator: React.FC<FormSeparatorProps> = ({ style }) => {
  const { theme } = useTheme();

  const getSeparatorStyle = (): ViewStyle => {
    return {
      height: 1,
      backgroundColor: theme.colors.border,
      marginVertical: 24,
    };
  };

  return <View style={[getSeparatorStyle(), style]} />;
};

const styles = StyleSheet.create({
  form: {
    flex: 1,
  },
  field: {
    marginBottom: 16,
  },
  section: {
    marginBottom: 32,
  },
});

export default Form;
