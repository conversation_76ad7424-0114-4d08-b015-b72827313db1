import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import Button from './ui/Button';
import Input from './ui/Input';
import { Form, FormField, FormLabel, FormMessage, FormSection, FormActions } from './ui/Form';
import Card from './ui/Card';
import { Typography, Heading2, Body1 } from './ui/Typography';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react-native';

interface LoginFormData {
  email: string;
  password: string;
}

interface LoginFormErrors {
  email?: string;
  password?: string;
}

interface EnhancedLoginFormProps {
  onForgotPassword?: () => void;
  onRegister?: () => void;
  onSuccess?: () => void;
}

const EnhancedLoginForm: React.FC<EnhancedLoginFormProps> = ({
  onForgotPassword,
  onRegister,
  onSuccess,
}) => {
  const { theme } = useTheme();
  const { login } = useAuth();
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<LoginFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: LoginFormErrors = {};

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!enhancedApiClient.validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await login(formData.email.trim(), formData.password);

      if (result.success) {
        Alert.alert(
          'Login Successful',
          'Welcome back! You have been successfully logged in.',
          [
            {
              text: 'OK',
              onPress: () => {
                onSuccess?.();
              },
            },
          ]
        );
      } else {
        Alert.alert(
          'Login Failed',
          result.error || 'Invalid email or password. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field: keyof LoginFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Heading2 align="center" color="foreground">
          Welcome Back
        </Heading2>
        <Body1 align="center" color="mutedForeground" style={styles.subtitle}>
          Sign in to your Benzochem Industries account
        </Body1>
      </View>

      <Card variant="elevated" padding="lg" style={styles.card}>
        <Form>
          <FormSection>
            {/* Email Field */}
            <FormField>
              <FormLabel required>Email Address</FormLabel>
              <Input
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                placeholder="Enter your email address"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                error={errors.email}
                leftIcon={<Mail size={18} color={theme.colors.mutedForeground} />}
              />
              <FormMessage error={errors.email} />
            </FormField>

            {/* Password Field */}
            <FormField>
              <FormLabel required>Password</FormLabel>
              <Input
                value={formData.password}
                onChangeText={(value) => updateFormData('password', value)}
                placeholder="Enter your password"
                secureTextEntry={!showPassword}
                autoComplete="password"
                error={errors.password}
                leftIcon={<Lock size={18} color={theme.colors.mutedForeground} />}
                rightIcon={
                  <TouchableOpacity
                    onPress={() => setShowPassword(!showPassword)}
                    activeOpacity={0.7}
                  >
                    {showPassword ? (
                      <EyeOff size={18} color={theme.colors.mutedForeground} />
                    ) : (
                      <Eye size={18} color={theme.colors.mutedForeground} />
                    )}
                  </TouchableOpacity>
                }
              />
              <FormMessage error={errors.password} />
            </FormField>

            {/* Forgot Password Link */}
            {onForgotPassword && (
              <TouchableOpacity
                onPress={onForgotPassword}
                style={styles.forgotPasswordLink}
                activeOpacity={0.7}
              >
                <Text style={[styles.linkText, { color: theme.colors.primary }]}>
                  Forgot your password?
                </Text>
              </TouchableOpacity>
            )}

            <FormActions align="center">
              <Button
                title={isSubmitting ? 'Signing In...' : 'Sign In'}
                onPress={handleSubmit}
                loading={isSubmitting}
                disabled={isSubmitting}
                variant="primary"
                size="lg"
                fullWidth
              />
            </FormActions>
          </FormSection>
        </Form>
      </Card>

      {/* Register Link */}
      {onRegister && (
        <View style={styles.registerSection}>
          <Body1 align="center" color="mutedForeground">
            Don't have an account?{' '}
            <TouchableOpacity onPress={onRegister} activeOpacity={0.7}>
              <Text style={[styles.linkText, { color: theme.colors.primary }]}>
                Create Account
              </Text>
            </TouchableOpacity>
          </Body1>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 32,
    marginTop: 32,
  },
  subtitle: {
    marginTop: 8,
  },
  card: {
    marginBottom: 24,
  },
  forgotPasswordLink: {
    alignSelf: 'flex-end',
    marginTop: 8,
    marginBottom: 16,
  },
  linkText: {
    fontSize: 14,
    fontFamily: 'System',
    fontWeight: '500',
  },
  registerSection: {
    alignItems: 'center',
    paddingVertical: 16,
  },
});

export default EnhancedLoginForm;
