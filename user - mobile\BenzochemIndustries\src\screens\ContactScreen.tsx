import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import { Form, FormField, FormLabel, FormMessage, FormSection, FormActions, FormSeparator } from '../components/ui/Form';
import Card, { CardContent, CardHeader } from '../components/ui/Card';
import { Typography, Heading2, Heading3, Body1, Body2 } from '../components/ui/Typography';
import Badge from '../components/ui/Badge';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Building, 
  Globe, 
  Users, 
  Award, 
  Send,
  MessageSquare,
  User,
  CheckCircle,
  ExternalLink
} from 'lucide-react-native';

interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  inquiryType: string;
  message: string;
}

interface ContactFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  message?: string;
}

const inquiryTypes = [
  'General Inquiry',
  'Product Information',
  'Quotation Request',
  'Technical Support',
  'Partnership',
  'Other',
];

const contactMethods = [
  {
    icon: Building,
    title: "Headquarters",
    description: "Our main office and manufacturing facility is located in Gujarat, India, with additional distribution centers across North America, Europe, and Asia.",
    details: []
  },
  {
    icon: MapPin,
    title: "Address",
    description: "",
    details: [
      "123 Chemical Lane, Industrial District",
      "Gujarat 380001",
      "India"
    ]
  },
  {
    icon: Phone,
    title: "Phone",
    description: "",
    details: [
      "Main: +91 (234) 567-890",
      "Customer Service: +91 (234) 567-891",
      "Technical Support: +91 (234) 567-892"
    ]
  },
  {
    icon: Mail,
    title: "Email",
    description: "",
    details: [
      "General Inquiries: <EMAIL>",
      "Sales: <EMAIL>",
      "Support: <EMAIL>"
    ]
  },
  {
    icon: Clock,
    title: "Business Hours",
    description: "",
    details: [
      "Monday - Friday: 9:00 AM - 6:00 PM IST",
      "Saturday: 9:00 AM - 1:00 PM IST",
      "Sunday: Closed"
    ]
  }
];

const quickStats = [
  {
    icon: Globe,
    value: "28+",
    label: "Years Experience",
    description: "Industry expertise"
  },
  {
    icon: Users,
    value: "500+",
    label: "Happy Clients",
    description: "Worldwide"
  },
  {
    icon: Award,
    value: "ISO",
    label: "Certified",
    description: "Quality assured"
  }
];

const ContactScreen: React.FC = () => {
  const { theme } = useTheme();
  const [formData, setFormData] = useState<ContactFormData>({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    inquiryType: '',
    message: '',
  });
  const [errors, setErrors] = useState<ContactFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: ContactFormErrors = {};

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!enhancedApiClient.validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await enhancedApiClient.sendContactMessage({
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim(),
        company: formData.company.trim() || undefined,
        inquiryType: formData.inquiryType || undefined,
        message: formData.message.trim(),
      });

      if (response.success) {
        setIsSubmitted(true);
        // Reset form
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          company: '',
          inquiryType: '',
          message: '',
        });
        setErrors({});
      } else {
        Alert.alert(
          'Error',
          response.error || 'Failed to send message. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Contact form submission error:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field: keyof ContactFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof ContactFormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handlePhonePress = (phoneNumber: string) => {
    const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
    Linking.openURL(`tel:${cleanNumber}`);
  };

  const handleEmailPress = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  const renderContactMethod = (method: typeof contactMethods[0], index: number) => (
    <Card key={index} variant="outlined" padding="md" style={styles.contactMethodCard}>
      <View style={styles.contactMethodHeader}>
        <View style={[styles.contactMethodIcon, { backgroundColor: theme.colors.primary + '15' }]}>
          <method.icon size={24} color={theme.colors.primary} />
        </View>
        <Heading3 color="foreground">{method.title}</Heading3>
      </View>
      
      {method.description && (
        <Body2 color="mutedForeground" style={styles.contactMethodDescription}>
          {method.description}
        </Body2>
      )}
      
      {method.details.map((detail, detailIndex) => (
        <TouchableOpacity
          key={detailIndex}
          style={styles.contactDetail}
          onPress={() => {
            if (method.title === 'Phone') {
              handlePhonePress(detail);
            } else if (method.title === 'Email') {
              handleEmailPress(detail.split(': ')[1] || detail);
            }
          }}
          activeOpacity={method.title === 'Phone' || method.title === 'Email' ? 0.7 : 1}
        >
          <Body1 color="foreground" style={styles.contactDetailText}>
            {detail}
          </Body1>
          {(method.title === 'Phone' || method.title === 'Email') && (
            <ExternalLink size={16} color={theme.colors.mutedForeground} />
          )}
        </TouchableOpacity>
      ))}
    </Card>
  );

  const renderQuickStat = (stat: typeof quickStats[0], index: number) => (
    <Card key={index} variant="elevated" padding="md" style={styles.quickStatCard}>
      <View style={styles.quickStatContent}>
        <View style={[styles.quickStatIcon, { backgroundColor: theme.colors.primary + '15' }]}>
          <stat.icon size={20} color={theme.colors.primary} />
        </View>
        <Heading3 color="primary" style={styles.quickStatValue}>{stat.value}</Heading3>
        <Body1 color="foreground" weight="medium">{stat.label}</Body1>
        <Body2 color="mutedForeground">{stat.description}</Body2>
      </View>
    </Card>
  );

  if (isSubmitted) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Card variant="elevated" padding="lg" style={styles.successCard}>
            <View style={styles.successContent}>
              <CheckCircle size={64} color={theme.colors.success} />
              <Heading2 align="center" color="foreground" style={styles.successTitle}>
                Message Sent Successfully!
              </Heading2>
              <Body1 align="center" color="mutedForeground" style={styles.successMessage}>
                Thank you for contacting us! We have received your message and will get back to you within 24 hours.
              </Body1>
              <Button
                title="Send Another Message"
                onPress={() => setIsSubmitted(false)}
                variant="outline"
                size="lg"
                rightIcon={<MessageSquare size={16} color={theme.colors.primary} />}
                style={styles.successButton}
              />
            </View>
          </Card>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Badge variant="secondary" size="md" style={styles.headerBadge}>
            Get In Touch
          </Badge>
          <Heading2 align="center" color="foreground" style={styles.headerTitle}>
            Contact Us
          </Heading2>
          <Body1 align="center" color="mutedForeground" style={styles.headerDescription}>
            Get in touch with our team for inquiries, quotes, or technical support. We're here to help you find the perfect chemical solutions.
          </Body1>
        </View>

        {/* Contact Form */}
        <Card variant="elevated" padding="lg" style={styles.formCard}>
          <View style={styles.formHeader}>
            <View style={[styles.formHeaderBadge, { backgroundColor: theme.colors.primary + '15' }]}>
              <MessageSquare size={16} color={theme.colors.primary} />
              <Body2 color="primary" weight="medium">Contact Form</Body2>
            </View>
            <Heading3 color="foreground" align="center" style={styles.formTitle}>
              Let's Start a Conversation
            </Heading3>
            <Body1 color="mutedForeground" align="center" style={styles.formDescription}>
              Fill out the form below and we'll get back to you as soon as possible.
            </Body1>
          </View>

          <Form>
            <FormSection>
              {/* Name Fields */}
              <View style={styles.row}>
                <FormField style={styles.halfField}>
                  <FormLabel required>First Name</FormLabel>
                  <Input
                    value={formData.firstName}
                    onChangeText={(value) => updateFormData('firstName', value)}
                    placeholder="Enter your first name"
                    error={errors.firstName}
                    leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
                  />
                  <FormMessage error={errors.firstName} />
                </FormField>

                <FormField style={styles.halfField}>
                  <FormLabel required>Last Name</FormLabel>
                  <Input
                    value={formData.lastName}
                    onChangeText={(value) => updateFormData('lastName', value)}
                    placeholder="Enter your last name"
                    error={errors.lastName}
                    leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
                  />
                  <FormMessage error={errors.lastName} />
                </FormField>
              </View>

              {/* Email Field */}
              <FormField>
                <FormLabel required>Email</FormLabel>
                <Input
                  value={formData.email}
                  onChangeText={(value) => updateFormData('email', value)}
                  placeholder="Enter your email address"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  error={errors.email}
                  leftIcon={<Mail size={18} color={theme.colors.mutedForeground} />}
                />
                <FormMessage error={errors.email} />
              </FormField>

              {/* Company Field */}
              <FormField>
                <FormLabel>Company</FormLabel>
                <Input
                  value={formData.company}
                  onChangeText={(value) => updateFormData('company', value)}
                  placeholder="Enter your company name (optional)"
                  leftIcon={<Building size={18} color={theme.colors.mutedForeground} />}
                />
              </FormField>

              {/* Inquiry Type Field */}
              <FormField>
                <FormLabel>Inquiry Type</FormLabel>
                <Input
                  value={formData.inquiryType}
                  onChangeText={(value) => updateFormData('inquiryType', value)}
                  placeholder="Select or enter inquiry type (optional)"
                />
              </FormField>

              {/* Message Field */}
              <FormField>
                <FormLabel required>Message</FormLabel>
                <Input
                  value={formData.message}
                  onChangeText={(value) => updateFormData('message', value)}
                  placeholder="Enter your message (minimum 10 characters)"
                  multiline
                  numberOfLines={4}
                  error={errors.message}
                  leftIcon={<MessageSquare size={18} color={theme.colors.mutedForeground} />}
                  style={{ minHeight: 100 }}
                />
                <FormMessage error={errors.message} />
              </FormField>

              <FormActions align="center">
                <Button
                  title={isSubmitting ? 'Sending...' : 'Send Message'}
                  onPress={handleSubmit}
                  loading={isSubmitting}
                  disabled={isSubmitting}
                  variant="primary"
                  size="lg"
                  fullWidth
                  leftIcon={<Send size={16} color={theme.colors.primaryForeground} />}
                />
              </FormActions>
              
              <Body2 color="mutedForeground" align="center" style={styles.formNote}>
                * Required fields. We typically respond within 24 hours during business days.
              </Body2>
            </FormSection>
          </Form>
        </Card>

        {/* Contact Information */}
        <View style={styles.contactInfoSection}>
          <View style={styles.sectionHeader}>
            <View style={[styles.sectionBadge, { backgroundColor: theme.colors.primary + '15' }]}>
              <Phone size={16} color={theme.colors.primary} />
              <Body2 color="primary" weight="medium">Contact Information</Body2>
            </View>
            <Heading3 color="foreground" align="center">Get In Touch</Heading3>
            <Body1 color="mutedForeground" align="center">
              Multiple ways to reach our team. We're here to help with your chemical needs.
            </Body1>
          </View>

          <View style={styles.contactMethods}>
            {contactMethods.map(renderContactMethod)}
          </View>
        </View>

        {/* Quick Stats */}
        <View style={styles.quickStatsSection}>
          <Heading3 color="foreground" align="center" style={styles.quickStatsTitle}>
            Why Choose Benzochem Industries?
          </Heading3>
          <View style={styles.quickStats}>
            {quickStats.map(renderQuickStat)}
          </View>
        </View>

        {/* Quality Assurance */}
        <Card variant="elevated" padding="lg" style={styles.qualityCard}>
          <View style={styles.qualityContent}>
            <View style={[styles.qualityIcon, { backgroundColor: theme.colors.success + '15' }]}>
              <Award size={32} color={theme.colors.success} />
            </View>
            <Heading3 color="foreground" align="center" style={styles.qualityTitle}>
              Quality Assurance
            </Heading3>
            <Body1 color="mutedForeground" align="center" style={styles.qualityDescription}>
              ISO certified facility with 24/7 quality monitoring. All products come with certificates of analysis and technical support.
            </Body1>
            <View style={styles.qualityBadges}>
              <Badge variant="secondary" size="sm">ISO 9001:2015</Badge>
              <Badge variant="secondary" size="sm">24/7 Support</Badge>
              <Badge variant="secondary" size="sm">Global Shipping</Badge>
            </View>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    marginTop: 16,
  },
  headerBadge: {
    marginBottom: 16,
  },
  headerTitle: {
    marginBottom: 12,
  },
  headerDescription: {
    textAlign: 'center',
    lineHeight: 24,
  },
  formCard: {
    marginBottom: 32,
  },
  formHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  formHeaderBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 12,
  },
  formTitle: {
    marginBottom: 8,
  },
  formDescription: {
    textAlign: 'center',
    lineHeight: 22,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfField: {
    flex: 1,
  },
  formNote: {
    marginTop: 12,
    fontSize: 12,
  },
  successCard: {
    marginTop: 50,
  },
  successContent: {
    alignItems: 'center',
    gap: 16,
  },
  successTitle: {
    marginTop: 16,
  },
  successMessage: {
    textAlign: 'center',
    marginBottom: 8,
  },
  successButton: {
    marginTop: 16,
    minWidth: 200,
  },
  contactInfoSection: {
    marginBottom: 32,
  },
  sectionHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  sectionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 12,
  },
  contactMethods: {
    gap: 16,
  },
  contactMethodCard: {
    marginBottom: 0,
  },
  contactMethodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  contactMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contactMethodDescription: {
    marginBottom: 12,
    lineHeight: 20,
  },
  contactDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  contactDetailText: {
    flex: 1,
  },
  quickStatsSection: {
    marginBottom: 32,
  },
  quickStatsTitle: {
    marginBottom: 20,
  },
  quickStats: {
    flexDirection: 'row',
    gap: 12,
  },
  quickStatCard: {
    flex: 1,
  },
  quickStatContent: {
    alignItems: 'center',
    gap: 8,
  },
  quickStatIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickStatValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  qualityCard: {
    marginBottom: 32,
  },
  qualityContent: {
    alignItems: 'center',
    gap: 16,
  },
  qualityIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  qualityTitle: {
    marginBottom: 8,
  },
  qualityDescription: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 16,
  },
  qualityBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'center',
  },
});

export default ContactScreen;
