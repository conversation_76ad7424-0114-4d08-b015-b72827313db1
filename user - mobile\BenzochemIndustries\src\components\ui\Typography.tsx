import React from 'react';
import { Text, TextProps, StyleSheet, TextStyle } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface TypographyProps extends TextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'overline';
  color?: 'primary' | 'secondary' | 'muted' | 'destructive' | 'foreground' | 'mutedForeground';
  align?: 'left' | 'center' | 'right' | 'justify';
  weight?: 'regular' | 'medium' | 'semibold' | 'bold';
  children: React.ReactNode;
}

const Typography: React.FC<TypographyProps> = ({
  variant = 'body1',
  color = 'foreground',
  align = 'left',
  weight,
  style,
  children,
  ...props
}) => {
  const { theme } = useTheme();

  const getTypographyStyle = (): TextStyle => {
    // Base variant styles
    const variantStyles: Record<string, TextStyle> = {
      h1: {
        fontSize: 32,
        lineHeight: 40,
        fontFamily: theme.fonts.bold,
        letterSpacing: -0.02,
      },
      h2: {
        fontSize: 28,
        lineHeight: 36,
        fontFamily: theme.fonts.bold,
        letterSpacing: -0.02,
      },
      h3: {
        fontSize: 24,
        lineHeight: 32,
        fontFamily: theme.fonts.semibold,
        letterSpacing: -0.01,
      },
      h4: {
        fontSize: 20,
        lineHeight: 28,
        fontFamily: theme.fonts.semibold,
        letterSpacing: -0.01,
      },
      h5: {
        fontSize: 18,
        lineHeight: 26,
        fontFamily: theme.fonts.medium,
        letterSpacing: -0.01,
      },
      h6: {
        fontSize: 16,
        lineHeight: 24,
        fontFamily: theme.fonts.medium,
        letterSpacing: -0.01,
      },
      body1: {
        fontSize: 16,
        lineHeight: 24,
        fontFamily: theme.fonts.regular,
      },
      body2: {
        fontSize: 14,
        lineHeight: 20,
        fontFamily: theme.fonts.regular,
      },
      caption: {
        fontSize: 12,
        lineHeight: 16,
        fontFamily: theme.fonts.regular,
      },
      overline: {
        fontSize: 10,
        lineHeight: 16,
        fontFamily: theme.fonts.medium,
        textTransform: 'uppercase',
        letterSpacing: 1.5,
      },
    };

    // Color styles
    const colorStyles: Record<string, { color: string }> = {
      primary: { color: theme.colors.primary },
      secondary: { color: theme.colors.secondary },
      muted: { color: theme.colors.muted },
      destructive: { color: theme.colors.destructive },
      foreground: { color: theme.colors.foreground },
      mutedForeground: { color: theme.colors.mutedForeground },
    };

    // Weight override
    const weightStyles: Record<string, { fontFamily: string }> = {
      regular: { fontFamily: theme.fonts.regular },
      medium: { fontFamily: theme.fonts.medium },
      semibold: { fontFamily: theme.fonts.semibold },
      bold: { fontFamily: theme.fonts.bold },
    };

    // Alignment styles
    const alignStyles: Record<string, { textAlign: TextStyle['textAlign'] }> = {
      left: { textAlign: 'left' },
      center: { textAlign: 'center' },
      right: { textAlign: 'right' },
      justify: { textAlign: 'justify' },
    };

    return {
      ...variantStyles[variant],
      ...colorStyles[color],
      ...(weight ? weightStyles[weight] : {}),
      ...alignStyles[align],
    };
  };

  return (
    <Text style={[getTypographyStyle(), style]} {...props}>
      {children}
    </Text>
  );
};

// Convenience components for common typography patterns
export const Heading1: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h1" {...props} />
);

export const Heading2: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h2" {...props} />
);

export const Heading3: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h3" {...props} />
);

export const Heading4: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h4" {...props} />
);

export const Heading5: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h5" {...props} />
);

export const Heading6: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h6" {...props} />
);

export const Body1: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="body1" {...props} />
);

export const Body2: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="body2" {...props} />
);

export const Caption: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="caption" {...props} />
);

export const Overline: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="overline" {...props} />
);

export default Typography;
