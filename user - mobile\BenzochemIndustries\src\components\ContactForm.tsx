import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import Button from './ui/Button';
import Input from './ui/Input';
import { Form, FormField, FormLabel, FormMessage, FormSection, FormActions } from './ui/Form';
import Card from './ui/Card';
import { Mail, User, Building, MessageSquare } from 'lucide-react-native';

interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  inquiryType: string;
  message: string;
}

interface ContactFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  message?: string;
}

const inquiryTypes = [
  'General Inquiry',
  'Product Information',
  'Quotation Request',
  'Technical Support',
  'Partnership',
  'Other',
];

const ContactForm: React.FC = () => {
  const { theme } = useTheme();
  const [formData, setFormData] = useState<ContactFormData>({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    inquiryType: '',
    message: '',
  });
  const [errors, setErrors] = useState<ContactFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: ContactFormErrors = {};

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!enhancedApiClient.validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await enhancedApiClient.sendContactMessage({
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim(),
        company: formData.company.trim() || undefined,
        inquiryType: formData.inquiryType || undefined,
        message: formData.message.trim(),
      });

      if (response.success) {
        Alert.alert(
          'Message Sent',
          'Thank you for contacting us! We will get back to you within 24 hours.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Reset form
                setFormData({
                  firstName: '',
                  lastName: '',
                  email: '',
                  company: '',
                  inquiryType: '',
                  message: '',
                });
                setErrors({});
              },
            },
          ]
        );
      } else {
        Alert.alert(
          'Error',
          response.error || 'Failed to send message. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Contact form submission error:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field: keyof ContactFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof ContactFormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Card variant="elevated" padding="lg" style={styles.card}>
        <Form>
          <FormSection
            title="Contact Us"
            description="Get in touch with our team for any inquiries about our chemical products and services."
          >
            {/* Name Fields */}
            <View style={styles.row}>
              <FormField style={styles.halfField}>
                <FormLabel required>First Name</FormLabel>
                <Input
                  value={formData.firstName}
                  onChangeText={(value) => updateFormData('firstName', value)}
                  placeholder="Enter your first name"
                  error={errors.firstName}
                  leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
                />
                <FormMessage error={errors.firstName} />
              </FormField>

              <FormField style={styles.halfField}>
                <FormLabel required>Last Name</FormLabel>
                <Input
                  value={formData.lastName}
                  onChangeText={(value) => updateFormData('lastName', value)}
                  placeholder="Enter your last name"
                  error={errors.lastName}
                  leftIcon={<User size={18} color={theme.colors.mutedForeground} />}
                />
                <FormMessage error={errors.lastName} />
              </FormField>
            </View>

            {/* Email Field */}
            <FormField>
              <FormLabel required>Email</FormLabel>
              <Input
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                placeholder="Enter your email address"
                keyboardType="email-address"
                autoCapitalize="none"
                error={errors.email}
                leftIcon={<Mail size={18} color={theme.colors.mutedForeground} />}
              />
              <FormMessage error={errors.email} />
            </FormField>

            {/* Company Field */}
            <FormField>
              <FormLabel>Company</FormLabel>
              <Input
                value={formData.company}
                onChangeText={(value) => updateFormData('company', value)}
                placeholder="Enter your company name (optional)"
                leftIcon={<Building size={18} color={theme.colors.mutedForeground} />}
              />
            </FormField>

            {/* Inquiry Type Field */}
            <FormField>
              <FormLabel>Inquiry Type</FormLabel>
              <Input
                value={formData.inquiryType}
                onChangeText={(value) => updateFormData('inquiryType', value)}
                placeholder="Select or enter inquiry type (optional)"
              />
            </FormField>

            {/* Message Field */}
            <FormField>
              <FormLabel required>Message</FormLabel>
              <Input
                value={formData.message}
                onChangeText={(value) => updateFormData('message', value)}
                placeholder="Enter your message (minimum 10 characters)"
                multiline
                numberOfLines={4}
                error={errors.message}
                leftIcon={<MessageSquare size={18} color={theme.colors.mutedForeground} />}
                style={{ minHeight: 100 }}
              />
              <FormMessage error={errors.message} />
            </FormField>

            <FormActions align="right">
              <Button
                title={isSubmitting ? 'Sending...' : 'Send Message'}
                onPress={handleSubmit}
                loading={isSubmitting}
                disabled={isSubmitting}
                variant="primary"
                size="lg"
              />
            </FormActions>
          </FormSection>
        </Form>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 32,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfField: {
    flex: 1,
  },
});

export default ContactForm;
