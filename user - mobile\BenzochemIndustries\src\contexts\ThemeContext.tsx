import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import { PreferencesUtils } from '../utils/storage';
import { Theme, ThemeColors, ThemeContextType } from '../types';

const lightTheme: Theme = {
  dark: false,
  colors: {
    // Primary colors - Rich teal-green matching web project
    primary: '#2D5A4A', // oklch(0.35 0.08 165)
    primaryForeground: '#F5F1E8', // oklch(0.97 0.015 70)
    primaryDark: '#1F3D33', // Darker variant
    secondary: '#E1D5B7', // oklch(0.88 0.025 75)
    secondaryForeground: '#2D5A4A',

    // Background colors - Warm vanilla cream
    background: '#F5F1E8', // oklch(0.95 0.02 75)
    foreground: '#3A4A3A', // oklch(0.25 0.05 160)
    surface: '#F8F5F0', // oklch(0.97 0.015 70)
    card: '#F8F5F0', // oklch(0.97 0.015 70)
    cardForeground: '#3A4A3A',
    popover: '#F8F5F0',
    popoverForeground: '#3A4A3A',

    // Text colors
    text: '#3A4A3A', // oklch(0.25 0.05 160)
    textSecondary: '#6B7B6B', // oklch(0.5 0.04 160)

    // Muted colors
    muted: '#E1D5B7', // oklch(0.88 0.025 75)
    mutedForeground: '#6B7B6B', // oklch(0.5 0.04 160)
    accent: '#D9C7A3', // oklch(0.85 0.03 80)
    accentForeground: '#2D5A4A',

    // Border and input
    border: '#D4C4A8',
    input: '#E1D5B7',
    ring: '#2D5A4A',

    // UI colors
    notification: '#DC2626', // Red

    // Status colors
    error: '#DC2626',
    errorForeground: '#FFFFFF',
    success: '#16A34A',
    successForeground: '#FFFFFF',
    warning: '#D97706',
    warningForeground: '#FFFFFF',
    info: '#0EA5E9',
    infoForeground: '#FFFFFF',

    // Utility colors
    medium: '#6B7B6B',
    destructive: '#DC2626',
    destructiveForeground: '#FFFFFF',

    // Vanilla latte specific colors
    vanilla: {
      50: '#fefcfb',   // oklch(0.98 0.01 70) - Very light vanilla
      100: '#fdf8f6',  // oklch(0.95 0.02 75) - Light vanilla
      200: '#f9f1ec',  // oklch(0.9 0.025 75) - Vanilla
      300: '#f3e8e0',  // oklch(0.85 0.03 80) - Medium vanilla
      400: '#edddd3',  // oklch(0.8 0.035 80) - Darker vanilla
      500: '#e6d2c6',  // oklch(0.75 0.04 85) - Deep vanilla
      600: '#dfc7b9',  // oklch(0.7 0.045 85) - Rich vanilla
      700: '#d8bcac',  // oklch(0.65 0.05 90) - Dark vanilla
      800: '#d1b19f',  // oklch(0.6 0.055 90) - Very dark vanilla
      900: '#caa692',  // oklch(0.55 0.06 95) - Darkest vanilla
    },

    // Teal accent colors
    teal: {
      50: '#f0fdfa',   // oklch(0.95 0.02 170) - Very light teal
      100: '#ccfbf1',  // oklch(0.9 0.04 165) - Light teal
      200: '#99f6e4',  // oklch(0.8 0.06 165) - Teal
      300: '#5eead4',  // oklch(0.7 0.08 165) - Medium teal
      400: '#2dd4bf',  // oklch(0.6 0.1 165) - Bright teal
      500: '#14b8a6',  // oklch(0.5 0.12 165) - Standard teal
      600: '#0d9488',  // oklch(0.45 0.14 165) - Dark teal
      700: '#0f766e',  // oklch(0.4 0.16 165) - Darker teal
      800: '#115e59',  // oklch(0.35 0.18 165) - Very dark teal
      900: '#134e4a',  // oklch(0.3 0.2 165) - Darkest teal
    },
  },
  fonts: {
    regular: 'System',
    medium: 'System',
    semibold: 'System',
    bold: 'System',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
};

const darkTheme: Theme = {
  dark: true,
  colors: {
    // Primary colors - Softer, more similar to light theme
    primary: '#3D6B52', // Softer teal-green, closer to light theme
    primaryForeground: '#F8F5F0', // Warm off-white
    primaryDark: '#2D5A4A', // Same as light theme for consistency
    secondary: '#D4C4A8', // Softer beige, closer to light theme
    secondaryForeground: '#3A4A3A',

    // Background colors - Soft dark vanilla latte theme
    background: '#2C2A27', // Soft dark vanilla, not too dark
    foreground: '#F8F5F0', // Warm off-white
    surface: '#35322E', // Slightly lighter soft vanilla
    card: '#3A3732', // Medium soft vanilla
    cardForeground: '#F8F5F0',
    popover: '#3A3732',
    popoverForeground: '#F8F5F0',

    // Text colors - Softer contrast
    text: '#F8F5F0', // Warm off-white
    textSecondary: '#C4B59A', // Soft muted vanilla

    // Muted colors - Gentler tones
    muted: '#4A453F', // Soft muted vanilla
    mutedForeground: '#B8A892', // Warm muted text
    accent: '#524C45', // Soft accent vanilla
    accentForeground: '#F8F5F0',

    // Border and input - Softer borders
    border: '#524C45', // Soft border color
    input: '#4A453F', // Soft input background
    ring: '#3D6B52', // Primary color for focus rings

    // UI colors - Softer notification color
    notification: '#E07B5F', // Softer warm red

    // Status colors - Softer, more harmonious with vanilla theme
    error: '#E07B5F', // Softer red-orange
    errorForeground: '#F8F5F0',
    success: '#7A9B6A', // Softer green, harmonious with primary
    successForeground: '#F8F5F0',
    warning: '#D4A574', // Warm golden, similar to light theme
    warningForeground: '#3A4A3A',
    info: '#6B8A9B', // Softer blue-teal
    infoForeground: '#F8F5F0',

    // Utility colors
    medium: '#B8A892',
    destructive: '#E07B5F', // Softer destructive color
    destructiveForeground: '#F8F5F0',

    // Vanilla latte colors for dark mode (softer, more harmonious)
    vanilla: {
      50: '#2C2A27',   // Soft dark vanilla base
      100: '#35322E',  // Slightly lighter
      200: '#3A3732',  // Medium soft vanilla
      300: '#4A453F',  // Muted vanilla
      400: '#524C45',  // Soft accent vanilla
      500: '#5D564E',  // Medium vanilla
      600: '#6B6158',  // Lighter vanilla
      700: '#7A6F64',  // Light vanilla
      800: '#8A7D70',  // Very light vanilla
      900: '#9A8B7C',  // Lightest vanilla
    },

    // Teal accent colors for dark mode (softer, more harmonious)
    teal: {
      50: '#2D4A42',   // Soft dark teal
      100: '#3D6B52',  // Primary teal (matches primary color)
      200: '#4A7A61',  // Medium teal
      300: '#5A8A70',  // Lighter teal
      400: '#6B9B7F',  // Light teal
      500: '#7BAB8E',  // Very light teal
      600: '#8BBB9D',  // Soft light teal
      700: '#9BCCAC',  // Pale teal
      800: '#ABDDBB',  // Very pale teal
      900: '#BBEECA',  // Lightest teal
    },
  },
  fonts: {
    regular: 'System',
    medium: 'System',
    semibold: 'System',
    bold: 'System',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeMode] = useState<'light' | 'dark' | 'system'>('system');
  const [theme, setTheme] = useState<Theme>(systemColorScheme === 'dark' ? darkTheme : lightTheme);

  // Load theme preference from storage
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const preferences = await PreferencesUtils.getPreferences();
        const savedTheme = preferences.theme || 'system';
        setThemeMode(savedTheme);
        updateTheme(savedTheme);
      } catch (error) {
        console.error('Error loading theme preference:', error);
      }
    };

    loadThemePreference();
  }, []);

  // Update theme when system color scheme changes
  useEffect(() => {
    if (themeMode === 'system') {
      setTheme(systemColorScheme === 'dark' ? darkTheme : lightTheme);
    }
  }, [systemColorScheme, themeMode]);

  const updateTheme = (mode: 'light' | 'dark' | 'system') => {
    let newTheme: Theme;
    
    switch (mode) {
      case 'light':
        newTheme = lightTheme;
        break;
      case 'dark':
        newTheme = darkTheme;
        break;
      case 'system':
        newTheme = systemColorScheme === 'dark' ? darkTheme : lightTheme;
        break;
      default:
        newTheme = lightTheme;
    }
    
    setTheme(newTheme);
  };

  const setThemeModeAndSave = async (mode: 'light' | 'dark' | 'system') => {
    try {
      setThemeMode(mode);
      updateTheme(mode);
      
      // Save preference
      const preferences = await PreferencesUtils.getPreferences();
      await PreferencesUtils.setPreferences({
        ...preferences,
        theme: mode,
      });
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const toggleTheme = () => {
    const newMode = theme.dark ? 'light' : 'dark';
    setThemeModeAndSave(newMode);
  };

  const value = {
    theme,
    isDark: theme.dark,
    toggleTheme,
    setTheme: setThemeModeAndSave,
    themeMode,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};