import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import { PreferencesUtils } from '../utils/storage';
import { Theme, ThemeColors, ThemeContextType } from '../types';

const lightTheme: Theme = {
  dark: false,
  colors: {
    // Primary colors - Rich teal-green matching web project
    primary: '#2D5A4A', // oklch(0.35 0.08 165)
    primaryForeground: '#F5F1E8', // oklch(0.97 0.015 70)
    primaryDark: '#1F3D33', // Darker variant
    secondary: '#E1D5B7', // oklch(0.88 0.025 75)
    secondaryForeground: '#2D5A4A',

    // Background colors - Warm vanilla cream
    background: '#F5F1E8', // oklch(0.95 0.02 75)
    foreground: '#3A4A3A', // oklch(0.25 0.05 160)
    surface: '#F8F5F0', // oklch(0.97 0.015 70)
    card: '#F8F5F0', // oklch(0.97 0.015 70)
    cardForeground: '#3A4A3A',
    popover: '#F8F5F0',
    popoverForeground: '#3A4A3A',

    // Text colors
    text: '#3A4A3A', // oklch(0.25 0.05 160)
    textSecondary: '#6B7B6B', // oklch(0.5 0.04 160)

    // Muted colors
    muted: '#E1D5B7', // oklch(0.88 0.025 75)
    mutedForeground: '#6B7B6B', // oklch(0.5 0.04 160)
    accent: '#D9C7A3', // oklch(0.85 0.03 80)
    accentForeground: '#2D5A4A',

    // Border and input
    border: '#D4C4A8',
    input: '#E1D5B7',
    ring: '#2D5A4A',

    // UI colors
    notification: '#DC2626', // Red

    // Status colors
    error: '#DC2626',
    errorForeground: '#FFFFFF',
    success: '#16A34A',
    successForeground: '#FFFFFF',
    warning: '#D97706',
    warningForeground: '#FFFFFF',
    info: '#0EA5E9',
    infoForeground: '#FFFFFF',

    // Utility colors
    medium: '#6B7B6B',
    destructive: '#DC2626',
    destructiveForeground: '#FFFFFF',

    // Vanilla latte specific colors
    vanilla: {
      50: '#fefcfb',   // oklch(0.98 0.01 70) - Very light vanilla
      100: '#fdf8f6',  // oklch(0.95 0.02 75) - Light vanilla
      200: '#f9f1ec',  // oklch(0.9 0.025 75) - Vanilla
      300: '#f3e8e0',  // oklch(0.85 0.03 80) - Medium vanilla
      400: '#edddd3',  // oklch(0.8 0.035 80) - Darker vanilla
      500: '#e6d2c6',  // oklch(0.75 0.04 85) - Deep vanilla
      600: '#dfc7b9',  // oklch(0.7 0.045 85) - Rich vanilla
      700: '#d8bcac',  // oklch(0.65 0.05 90) - Dark vanilla
      800: '#d1b19f',  // oklch(0.6 0.055 90) - Very dark vanilla
      900: '#caa692',  // oklch(0.55 0.06 95) - Darkest vanilla
    },

    // Teal accent colors
    teal: {
      50: '#f0fdfa',   // oklch(0.95 0.02 170) - Very light teal
      100: '#ccfbf1',  // oklch(0.9 0.04 165) - Light teal
      200: '#99f6e4',  // oklch(0.8 0.06 165) - Teal
      300: '#5eead4',  // oklch(0.7 0.08 165) - Medium teal
      400: '#2dd4bf',  // oklch(0.6 0.1 165) - Bright teal
      500: '#14b8a6',  // oklch(0.5 0.12 165) - Standard teal
      600: '#0d9488',  // oklch(0.45 0.14 165) - Dark teal
      700: '#0f766e',  // oklch(0.4 0.16 165) - Darker teal
      800: '#115e59',  // oklch(0.35 0.18 165) - Very dark teal
      900: '#134e4a',  // oklch(0.3 0.2 165) - Darkest teal
    },
  },
  fonts: {
    regular: 'System',
    medium: 'System',
    semibold: 'System',
    bold: 'System',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
};

const darkTheme: Theme = {
  dark: true,
  colors: {
    // Primary colors - Professional teal-green for chemical industry
    primary: '#10B981', // Modern emerald green - professional and vibrant
    primaryForeground: '#FFFFFF', // Pure white for maximum contrast
    primaryDark: '#059669', // Darker emerald for depth
    secondary: '#6B7280', // Professional neutral gray
    secondaryForeground: '#F9FAFB',

    // Background colors - Modern dark theme
    background: '#0F172A', // Deep slate blue - professional and modern
    foreground: '#F8FAFC', // Off-white with slight blue tint
    surface: '#1E293B', // Slate gray surface
    card: '#1E293B', // Same as surface for consistency
    cardForeground: '#F8FAFC',
    popover: '#1E293B',
    popoverForeground: '#F8FAFC',

    // Text colors - High contrast for readability
    text: '#F8FAFC', // Primary text - off-white
    textSecondary: '#94A3B8', // Secondary text - muted slate

    // Muted colors - Professional grays
    muted: '#334155', // Medium slate for muted backgrounds
    mutedForeground: '#94A3B8', // Muted text color
    accent: '#475569', // Accent slate
    accentForeground: '#F8FAFC',

    // Border and input - Subtle but visible
    border: '#334155', // Subtle border color
    input: '#334155', // Input background
    ring: '#10B981', // Primary color for focus rings

    // UI colors - Professional notification color
    notification: '#EF4444', // Modern red for notifications

    // Status colors - Modern, professional palette
    error: '#EF4444', // Modern red
    errorForeground: '#FFFFFF',
    success: '#10B981', // Same as primary for consistency
    successForeground: '#FFFFFF',
    warning: '#F59E0B', // Modern amber
    warningForeground: '#FFFFFF',
    info: '#3B82F6', // Modern blue
    infoForeground: '#FFFFFF',

    // Utility colors
    medium: '#94A3B8',
    destructive: '#EF4444', // Modern red
    destructiveForeground: '#FFFFFF',

    // Professional slate colors for dark mode
    vanilla: {
      50: '#0F172A',   // Deep slate (background)
      100: '#1E293B',  // Slate surface
      200: '#334155',  // Medium slate
      300: '#475569',  // Accent slate
      400: '#64748B',  // Light slate
      500: '#94A3B8',  // Muted slate
      600: '#CBD5E1',  // Very light slate
      700: '#E2E8F0',  // Pale slate
      800: '#F1F5F9',  // Off-white slate
      900: '#F8FAFC',  // Lightest slate
    },

    // Modern emerald colors for dark mode
    teal: {
      50: '#064E3B',   // Dark emerald
      100: '#065F46',  // Deep emerald
      200: '#047857',  // Medium emerald
      300: '#059669',  // Standard emerald
      400: '#10B981',  // Primary emerald
      500: '#34D399',  // Light emerald
      600: '#6EE7B7',  // Very light emerald
      700: '#A7F3D0',  // Pale emerald
      800: '#D1FAE5',  // Very pale emerald
      900: '#ECFDF5',  // Lightest emerald
    },
  },
  fonts: {
    regular: 'System',
    medium: 'System',
    semibold: 'System',
    bold: 'System',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeMode] = useState<'light' | 'dark' | 'system'>('system');
  const [theme, setTheme] = useState<Theme>(systemColorScheme === 'dark' ? darkTheme : lightTheme);

  // Load theme preference from storage
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const preferences = await PreferencesUtils.getPreferences();
        const savedTheme = preferences.theme || 'system';
        setThemeMode(savedTheme);
        updateTheme(savedTheme);
      } catch (error) {
        console.error('Error loading theme preference:', error);
      }
    };

    loadThemePreference();
  }, []);

  // Update theme when system color scheme changes
  useEffect(() => {
    if (themeMode === 'system') {
      setTheme(systemColorScheme === 'dark' ? darkTheme : lightTheme);
    }
  }, [systemColorScheme, themeMode]);

  const updateTheme = (mode: 'light' | 'dark' | 'system') => {
    let newTheme: Theme;
    
    switch (mode) {
      case 'light':
        newTheme = lightTheme;
        break;
      case 'dark':
        newTheme = darkTheme;
        break;
      case 'system':
        newTheme = systemColorScheme === 'dark' ? darkTheme : lightTheme;
        break;
      default:
        newTheme = lightTheme;
    }
    
    setTheme(newTheme);
  };

  const setThemeModeAndSave = async (mode: 'light' | 'dark' | 'system') => {
    try {
      setThemeMode(mode);
      updateTheme(mode);
      
      // Save preference
      const preferences = await PreferencesUtils.getPreferences();
      await PreferencesUtils.setPreferences({
        ...preferences,
        theme: mode,
      });
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const toggleTheme = () => {
    const newMode = theme.dark ? 'light' : 'dark';
    setThemeModeAndSave(newMode);
  };

  const value = {
    theme,
    isDark: theme.dark,
    toggleTheme,
    setTheme: setThemeModeAndSave,
    themeMode,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};