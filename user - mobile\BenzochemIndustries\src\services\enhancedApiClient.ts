import { Product, ApiProduct, UserData, Collection, Quotation } from '../types';
import {
  ADMIN_API_CONFIG,
  WEB_API_CONFIG,
  REQUEST_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  GST_VERIFICATION_CONFIG,
  VALIDATION_PATTERNS,
  API_ENDPOINTS,
  ENVIRONMENT
} from '../config';

/**
 * Enhanced API Client that mirrors the web project's API functionality
 * Provides comprehensive API integration for all web project endpoints
 */
class EnhancedApiClient {
  private baseUrl: string;
  private adminUrl: string;
  private apiKey: string;
  private timeout: number;

  constructor() {
    this.baseUrl = ENVIRONMENT.WEB_API_URL;
    this.adminUrl = ENVIRONMENT.ADMIN_API_URL;
    this.apiKey = ADMIN_API_CONFIG.API_KEY;
    this.timeout = REQUEST_CONFIG.TIMEOUT;

    console.log('Enhanced API Client initialized with:');
    console.log('- Web API URL:', this.baseUrl);
    console.log('- Admin API URL:', this.adminUrl);
    console.log('- API Key configured:', !!this.apiKey);
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    useAdmin = false
  ): Promise<{ success: boolean; data?: T; error?: string; pagination?: any }> {
    try {
      const url = `${useAdmin ? this.adminUrl : this.baseUrl}${endpoint}`;

      console.log('Making API request to:', url);
      console.log('Using admin API:', useAdmin);

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const headers = useAdmin ? REQUEST_CONFIG.ADMIN_HEADERS : REQUEST_CONFIG.DEFAULT_HEADERS;

      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log('Response status:', response.status, response.statusText);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        
        // Handle specific HTTP status codes
        switch (response.status) {
          case 401:
            errorMessage = ERROR_MESSAGES.API.UNAUTHORIZED;
            break;
          case 403:
            errorMessage = ERROR_MESSAGES.API.FORBIDDEN;
            break;
          case 404:
            errorMessage = ERROR_MESSAGES.API.NOT_FOUND;
            break;
          case 500:
          case 502:
          case 503:
          case 504:
            errorMessage = ERROR_MESSAGES.NETWORK.SERVER_ERROR;
            break;
          default:
            try {
              const errorResult = await response.json();
              errorMessage = errorResult.error || errorResult.message || errorMessage;
            } catch {
              errorMessage = response.statusText || errorMessage;
            }
        }
        
        throw new Error(errorMessage);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      
      let errorMessage = ERROR_MESSAGES.NETWORK.CONNECTION_FAILED;
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = ERROR_MESSAGES.NETWORK.TIMEOUT;
        } else if (error.message.includes('Failed to fetch') || error.name === 'TypeError') {
          errorMessage = useAdmin 
            ? ERROR_MESSAGES.API.ADMIN_CONNECTION_FAILED
            : ERROR_MESSAGES.API.WEB_CONNECTION_FAILED;
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  // =============================================================================
  // AUTHENTICATION API
  // =============================================================================
  
  async login(email: string, password: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.AUTH.LOGIN, {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phone?: string;
    businessName?: string;
    gstNumber?: string;
    legalNameOfBusiness?: string;
    tradeName?: string;
    dateOfRegistration?: string;
    constitutionOfBusiness?: string;
    taxpayerType?: string;
    principalPlaceOfBusiness?: string;
    natureOfCoreBusinessActivity?: string;
    gstStatus?: string;
    agreedToEmailMarketing?: boolean;
    agreedToSmsMarketing?: boolean;
  }) {
    return this.makeRequest(API_ENDPOINTS.WEB.AUTH.REGISTER, {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async forgotPassword(email: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.AUTH.FORGOT_PASSWORD, {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async resetPassword(token: string, newPassword: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.AUTH.RESET_PASSWORD, {
      method: 'POST',
      body: JSON.stringify({ token, newPassword }),
    });
  }

  // =============================================================================
  // PRODUCTS API
  // =============================================================================
  
  async getProducts(params: {
    limit?: number;
    offset?: number;
    search?: string;
    collection?: string;
    featured?: boolean;
  } = {}) {
    const searchParams = new URLSearchParams();

    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.search) searchParams.set('search', params.search);
    if (params.collection) searchParams.set('collection', params.collection);
    if (params.featured) searchParams.set('featured', 'true');

    const query = searchParams.toString();
    const endpoint = `${API_ENDPOINTS.WEB.PRODUCTS}${query ? `?${query}` : ''}`;

    return this.makeRequest(endpoint);
  }

  async getProductById(productId: string) {
    return this.makeRequest(`${API_ENDPOINTS.WEB.PRODUCTS}/${productId}`);
  }

  async getFeaturedProducts(): Promise<Product[]> {
    try {
      const response = await this.getProducts({ featured: true, limit: 8 });
      if (response.success && response.data && Array.isArray(response.data)) {
        return (response.data as ApiProduct[]).map(this.transformApiProductToProduct);
      }
      return [];
    } catch (error) {
      console.error('Error fetching featured products:', error);
      return [];
    }
  }

  async searchProducts(query: string): Promise<Product[]> {
    try {
      const response = await this.getProducts({ search: query, limit: 20 });
      if (response.success && response.data && Array.isArray(response.data)) {
        return (response.data as ApiProduct[]).map(this.transformApiProductToProduct);
      }
      return [];
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  }

  async getProductsByCollection(collectionTitle: string): Promise<Product[]> {
    try {
      const response = await this.getProducts({ collection: collectionTitle, limit: 100 });
      if (response.success && response.data && Array.isArray(response.data)) {
        return (response.data as ApiProduct[]).map(this.transformApiProductToProduct);
      }
      return [];
    } catch (error) {
      console.error(`Error fetching products for collection ${collectionTitle}:`, error);
      return [];
    }
  }

  // =============================================================================
  // COLLECTIONS API
  // =============================================================================
  
  async getCollections(params: {
    limit?: number;
    offset?: number;
    search?: string;
    visible?: boolean;
  } = {}) {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.search) searchParams.set('search', params.search);
    if (params.visible !== undefined) searchParams.set('visible', params.visible.toString());
    
    const query = searchParams.toString();
    const endpoint = `/collections${query ? `?${query}` : ''}`;
    
    return this.makeRequest(endpoint, {}, true); // Use admin API for collections
  }

  async initCollections() {
    return this.makeRequest(API_ENDPOINTS.WEB.INIT_COLLECTIONS, {
      method: 'POST',
    });
  }

  // =============================================================================
  // QUOTATIONS API
  // =============================================================================
  
  async createQuotation(quotationData: {
    userId: string;
    userEmail: string;
    userName: string;
    userPhone?: string;
    businessName?: string;
    products: Array<{
      productId: string;
      productName: string;
      quantity: string;
      unit: string;
      specifications?: string;
    }>;
    additionalRequirements?: string;
    deliveryLocation?: string;
    urgency?: 'standard' | 'urgent' | 'asap';
  }) {
    return this.makeRequest(API_ENDPOINTS.WEB.QUOTATIONS, {
      method: 'POST',
      body: JSON.stringify(quotationData),
    });
  }

  async getUserQuotations(userId: string, params: {
    limit?: number;
    offset?: number;
    status?: string;
  } = {}) {
    const searchParams = new URLSearchParams();
    searchParams.set('userId', userId);
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.status) searchParams.set('status', params.status);
    
    const query = searchParams.toString();
    return this.makeRequest(`${API_ENDPOINTS.WEB.QUOTATIONS}?${query}`);
  }

  async getQuotation(quotationId: string) {
    return this.makeRequest(`${API_ENDPOINTS.WEB.QUOTATIONS}/${quotationId}`);
  }

  // =============================================================================
  // CONTACT API
  // =============================================================================

  async sendContactMessage(contactData: {
    firstName: string;
    lastName: string;
    email: string;
    company?: string;
    inquiryType?: string;
    message: string;
  }) {
    return this.makeRequest(API_ENDPOINTS.WEB.CONTACT, {
      method: 'POST',
      body: JSON.stringify(contactData),
    });
  }

  // =============================================================================
  // SEARCH API
  // =============================================================================

  async getSearchSuggestions(query: string) {
    if (!query || query.trim().length < 2) {
      return { success: true, data: [] };
    }

    return this.makeRequest(`${API_ENDPOINTS.WEB.SEARCH_SUGGESTIONS}?q=${encodeURIComponent(query.trim())}`);
  }

  async getSearchHistory() {
    return this.makeRequest(API_ENDPOINTS.WEB.SEARCH_HISTORY);
  }

  async addToSearchHistory(query: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.SEARCH_HISTORY, {
      method: 'POST',
      body: JSON.stringify({ query }),
    });
  }

  // =============================================================================
  // ADDRESS SEARCH API
  // =============================================================================

  async getAddressSuggestions(query: string) {
    if (!query || query.length < 3) {
      return { success: true, suggestions: [] };
    }

    return this.makeRequest(`${API_ENDPOINTS.WEB.ADDRESS_SEARCH}?q=${encodeURIComponent(query)}`);
  }

  // =============================================================================
  // VERIFICATION APIs
  // =============================================================================

  async verifyGST(gstNumber: string): Promise<{ success: boolean; error?: string; data?: any }> {
    // Validate GST number format using config pattern
    if (!VALIDATION_PATTERNS.GST_NUMBER.test(gstNumber)) {
      return {
        success: false,
        error: ERROR_MESSAGES.VALIDATION.INVALID_GST,
      };
    }

    try {
      const response = await this.makeRequest(API_ENDPOINTS.WEB.GST_VERIFICATION, {
        method: 'POST',
        body: JSON.stringify({ gstNumber }),
      });

      return response;
    } catch (error) {
      return {
        success: false,
        error: "GST verification service unavailable",
      };
    }
  }

  async verifyEmail(email: string, token: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.EMAIL_VERIFICATION, {
      method: 'POST',
      body: JSON.stringify({ email, token }),
    });
  }

  async verifyPhone(phone: string, otp: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.PHONE_VERIFICATION, {
      method: 'POST',
      body: JSON.stringify({ phone, otp }),
    });
  }

  // =============================================================================
  // EMAIL SERVICES API
  // =============================================================================

  async subscribeToNewsletter(email: string, firstName?: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.NEWSLETTER, {
      method: 'POST',
      body: JSON.stringify({ email, firstName }),
    });
  }

  async sendWelcomeEmail(email: string, firstName: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.WELCOME_EMAIL, {
      method: 'POST',
      body: JSON.stringify({ email, firstName }),
    });
  }

  async sendAccountStatusEmail(email: string, firstName: string, status: string) {
    return this.makeRequest(API_ENDPOINTS.WEB.ACCOUNT_STATUS_EMAIL, {
      method: 'POST',
      body: JSON.stringify({ email, firstName, status }),
    });
  }

  async sendQuotationEmail(email: string, firstName: string, quotationId: string, type: 'created' | 'updated' | 'approved' | 'rejected') {
    return this.makeRequest(API_ENDPOINTS.WEB.QUOTATION_EMAIL, {
      method: 'POST',
      body: JSON.stringify({ email, firstName, quotationId, type }),
    });
  }

  // =============================================================================
  // CONSENT AND PRIVACY API
  // =============================================================================

  async updateCookieConsent(preferences: any) {
    return this.makeRequest(API_ENDPOINTS.WEB.COOKIE_CONSENT, {
      method: 'POST',
      body: JSON.stringify(preferences),
    });
  }

  async linkConsent(consentData: any) {
    return this.makeRequest(API_ENDPOINTS.WEB.LINK_CONSENT, {
      method: 'POST',
      body: JSON.stringify(consentData),
    });
  }

  // =============================================================================
  // USER MANAGEMENT API (Admin)
  // =============================================================================

  async getUserByEmail(email: string) {
    return this.makeRequest(`${API_ENDPOINTS.ADMIN.USERS}?email=${encodeURIComponent(email)}`, {}, true);
  }

  async createUser(userData: {
    userId: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    businessName?: string;
    gstNumber?: string;
    legalNameOfBusiness?: string;
    tradeName?: string;
    dateOfRegistration?: string;
    constitutionOfBusiness?: string;
    taxpayerType?: string;
    principalPlaceOfBusiness?: string;
    natureOfCoreBusinessActivity?: string;
    gstStatus?: string;
    agreedToEmailMarketing?: boolean;
    agreedToSmsMarketing?: boolean;
  }) {
    return this.makeRequest(API_ENDPOINTS.ADMIN.USERS, {
      method: 'POST',
      body: JSON.stringify(userData),
    }, true);
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  // Transform API product data to match the expected Product interface
  transformApiProductToProduct(apiProduct: ApiProduct): Product {
    return {
      id: apiProduct.id,
      title: apiProduct.title,
      description: apiProduct.description,
      descriptionHtml: apiProduct.description,
      tags: apiProduct.tags || [],
      quantity: apiProduct.quantity,
      collections: {
        edges: (apiProduct.collections || []).map((collection: string) => ({
          node: { title: collection }
        }))
      },
      images: {
        edges: (apiProduct.images || []).map((image: any) => ({
          node: { url: image.url || image }
        }))
      },
      media: {
        edges: (apiProduct.images || []).map((image: any, index: number) => ({
          node: {
            id: `media_${index}`,
            image: { url: image.url || image }
          }
        }))
      },
      priceRange: {
        minVariantPrice: {
          amount: apiProduct.priceRange?.minVariantPrice?.amount || '0.00',
          currencyCode: apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'
        },
        maxVariantPrice: {
          amount: apiProduct.priceRange?.maxVariantPrice?.amount || apiProduct.priceRange?.minVariantPrice?.amount || '0.00',
          currencyCode: apiProduct.priceRange?.maxVariantPrice?.currencyCode || apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'
        }
      },
      compareAtPriceRange: {
        minVariantPrice: {
          amount: '0.00',
          currencyCode: 'USD'
        },
        maxVariantPrice: {
          amount: '0.00',
          currencyCode: 'USD'
        }
      },
      metafields: [
        ...(apiProduct.purity ? [{
          id: 'purity',
          key: 'purity',
          namespace: 'chemical',
          value: apiProduct.purity,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.casNumber ? [{
          id: 'cas_number',
          key: 'cas_number',
          namespace: 'chemical',
          value: apiProduct.casNumber,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.molecularFormula ? [{
          id: 'molecular_formula',
          key: 'molecular_formula',
          namespace: 'chemical',
          value: apiProduct.molecularFormula,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.molecularWeight ? [{
          id: 'molecular_weight',
          key: 'molecular_weight',
          namespace: 'chemical',
          value: apiProduct.molecularWeight,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.appearance ? [{
          id: 'appearance',
          key: 'appearance',
          namespace: 'chemical',
          value: apiProduct.appearance,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.solubility ? [{
          id: 'solubility',
          key: 'solubility',
          namespace: 'chemical',
          value: apiProduct.solubility,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.phValue ? [{
          id: 'ph_value',
          key: 'ph_value',
          namespace: 'chemical',
          value: apiProduct.phValue,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.hsnNumber ? [{
          id: 'hsn_number',
          key: 'hsn_number',
          namespace: 'chemical',
          value: apiProduct.hsnNumber,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.chemicalName ? [{
          id: 'chemical_name',
          key: 'chemical_name',
          namespace: 'chemical',
          value: apiProduct.chemicalName,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.packaging ? [{
          id: 'packaging',
          key: 'packaging',
          namespace: 'chemical',
          value: JSON.stringify(apiProduct.packaging),
          type: 'json'
        }] : []),
        ...(apiProduct.features ? [{
          id: 'features',
          key: 'features',
          namespace: 'chemical',
          value: JSON.stringify(apiProduct.features),
          type: 'json'
        }] : []),
        ...(apiProduct.applications ? [{
          id: 'applications',
          key: 'applications',
          namespace: 'chemical',
          value: JSON.stringify(apiProduct.applications),
          type: 'json'
        }] : []),
        ...(apiProduct.applicationDetails ? [{
          id: 'application_details',
          key: 'application_details',
          namespace: 'chemical',
          value: JSON.stringify(apiProduct.applicationDetails),
          type: 'json'
        }] : [])
      ]
    };
  }

  // Validation helpers
  validateEmail(email: string): boolean {
    return VALIDATION_PATTERNS.EMAIL.test(email);
  }

  validatePhone(phone: string): boolean {
    return VALIDATION_PATTERNS.PHONE.test(phone);
  }

  validateGSTNumber(gstNumber: string): boolean {
    return VALIDATION_PATTERNS.GST_NUMBER.test(gstNumber);
  }

  // Error handling helpers
  getErrorMessage(error: any): string {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.error) return error.error;
    return ERROR_MESSAGES.NETWORK.CONNECTION_FAILED;
  }

  // Success message helpers
  getSuccessMessage(type: string, action: string): string {
    const messages = SUCCESS_MESSAGES as any;
    return messages[type]?.[action] || 'Operation completed successfully';
  }
}

// Export singleton instance
export const enhancedApiClient = new EnhancedApiClient();
export default enhancedApiClient;
