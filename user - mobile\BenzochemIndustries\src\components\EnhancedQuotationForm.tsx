import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { useQuotation } from '../contexts/QuotationContext';
import { enhancedApiClient } from '../services/enhancedApiClient';
import Button from './ui/Button';
import Input from './ui/Input';
import { Form, FormField, FormLabel, FormMessage, FormSection, FormActions, FormSeparator } from './ui/Form';
import Card from './ui/Card';
import { Typography, Heading2, Body1, Body2 } from './ui/Typography';
import Badge from './ui/Badge';
import { Package, Plus, Minus, MapPin, Clock, FileText, Trash2 } from 'lucide-react-native';

interface QuotationItem {
  productId: string;
  productName: string;
  quantity: string;
  unit: string;
  specifications?: string;
}

interface QuotationFormData {
  items: QuotationItem[];
  additionalRequirements: string;
  deliveryLocation: string;
  urgency: 'standard' | 'urgent' | 'asap';
}

interface QuotationFormErrors {
  items?: string;
  deliveryLocation?: string;
  additionalRequirements?: string;
}

interface EnhancedQuotationFormProps {
  initialItems?: QuotationItem[];
  onSuccess?: (quotationId: string) => void;
  onCancel?: () => void;
}

const EnhancedQuotationForm: React.FC<EnhancedQuotationFormProps> = ({
  initialItems = [],
  onSuccess,
  onCancel,
}) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  const { items: quotationItems, clearItems } = useQuotation();
  
  const [formData, setFormData] = useState<QuotationFormData>({
    items: initialItems.length > 0 ? initialItems : quotationItems,
    additionalRequirements: '',
    deliveryLocation: '',
    urgency: 'standard',
  });
  const [errors, setErrors] = useState<QuotationFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update items when quotation context changes
  useEffect(() => {
    if (initialItems.length === 0 && quotationItems.length > 0) {
      setFormData(prev => ({ ...prev, items: quotationItems }));
    }
  }, [quotationItems, initialItems]);

  const validateForm = (): boolean => {
    const newErrors: QuotationFormErrors = {};

    // Items validation
    if (formData.items.length === 0) {
      newErrors.items = 'At least one product is required for quotation';
    } else {
      // Validate each item
      const invalidItems = formData.items.some(item => 
        !item.productName.trim() || 
        !item.quantity.trim() || 
        parseFloat(item.quantity) <= 0
      );
      
      if (invalidItems) {
        newErrors.items = 'All items must have valid product names and quantities';
      }
    }

    // Delivery location validation
    if (!formData.deliveryLocation.trim()) {
      newErrors.deliveryLocation = 'Delivery location is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    if (!user) {
      Alert.alert('Authentication Required', 'Please log in to submit a quotation request.');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await enhancedApiClient.createQuotation({
        userId: user.id,
        userEmail: user.email,
        userName: `${user.firstName} ${user.lastName}`,
        userPhone: user.phone,
        businessName: user.businessName,
        products: formData.items.map(item => ({
          productId: item.productId,
          productName: item.productName,
          quantity: item.quantity,
          unit: item.unit,
          specifications: item.specifications,
        })),
        additionalRequirements: formData.additionalRequirements.trim() || undefined,
        deliveryLocation: formData.deliveryLocation.trim(),
        urgency: formData.urgency,
      });

      if (response.success) {
        // Clear quotation items from context
        clearItems();
        
        Alert.alert(
          'Quotation Submitted',
          'Your quotation request has been submitted successfully. We will get back to you within 24 hours.',
          [
            {
              text: 'OK',
              onPress: () => {
                onSuccess?.(response.data?.id || '');
              },
            },
          ]
        );
      } else {
        Alert.alert(
          'Submission Failed',
          response.error || 'Failed to submit quotation. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Quotation submission error:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const addItem = () => {
    const newItem: QuotationItem = {
      productId: '',
      productName: '',
      quantity: '1',
      unit: 'kg',
      specifications: '',
    };
    
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const updateItem = (index: number, field: keyof QuotationItem, value: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      ),
    }));
    
    // Clear items error when user adds/modifies items
    if (errors.items) {
      setErrors(prev => ({ ...prev, items: undefined }));
    }
  };

  const updateFormData = (field: keyof Omit<QuotationFormData, 'items'>, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof QuotationFormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const getUrgencyBadgeVariant = (urgency: string) => {
    switch (urgency) {
      case 'asap':
        return 'destructive';
      case 'urgent':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const getUrgencyLabel = (urgency: string) => {
    switch (urgency) {
      case 'asap':
        return 'ASAP';
      case 'urgent':
        return 'Urgent';
      default:
        return 'Standard';
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Heading2 align="center" color="foreground">
          Request Quotation
        </Heading2>
        <Body1 align="center" color="mutedForeground" style={styles.subtitle}>
          Get competitive prices for your chemical requirements
        </Body1>
      </View>

      <Card variant="elevated" padding="lg" style={styles.card}>
        <Form>
          <FormSection title="Products" description="Add the products you need quotations for">
            {/* Items List */}
            {formData.items.map((item, index) => (
              <Card key={index} variant="outlined" padding="md" style={styles.itemCard}>
                <View style={styles.itemHeader}>
                  <Package size={16} color={theme.colors.primary} />
                  <Text style={[styles.itemTitle, { color: theme.colors.foreground }]}>
                    Product {index + 1}
                  </Text>
                  {formData.items.length > 1 && (
                    <TouchableOpacity
                      onPress={() => removeItem(index)}
                      style={styles.removeButton}
                      activeOpacity={0.7}
                    >
                      <Trash2 size={16} color={theme.colors.destructive} />
                    </TouchableOpacity>
                  )}
                </View>

                <FormField>
                  <FormLabel required>Product Name</FormLabel>
                  <Input
                    value={item.productName}
                    onChangeText={(value) => updateItem(index, 'productName', value)}
                    placeholder="Enter product name"
                  />
                </FormField>

                <View style={styles.row}>
                  <FormField style={styles.quantityField}>
                    <FormLabel required>Quantity</FormLabel>
                    <Input
                      value={item.quantity}
                      onChangeText={(value) => updateItem(index, 'quantity', value)}
                      placeholder="0"
                      keyboardType="numeric"
                    />
                  </FormField>

                  <FormField style={styles.unitField}>
                    <FormLabel required>Unit</FormLabel>
                    <Input
                      value={item.unit}
                      onChangeText={(value) => updateItem(index, 'unit', value)}
                      placeholder="kg"
                    />
                  </FormField>
                </View>

                <FormField>
                  <FormLabel>Specifications</FormLabel>
                  <Input
                    value={item.specifications || ''}
                    onChangeText={(value) => updateItem(index, 'specifications', value)}
                    placeholder="Enter any specific requirements (optional)"
                    multiline
                    numberOfLines={2}
                  />
                </FormField>
              </Card>
            ))}

            <FormMessage error={errors.items} />

            {/* Add Item Button */}
            <Button
              title="Add Another Product"
              onPress={addItem}
              variant="outline"
              size="md"
              leftIcon={<Plus size={16} color={theme.colors.primary} />}
              style={styles.addButton}
            />
          </FormSection>

          <FormSeparator />

          <FormSection title="Delivery Details">
            {/* Delivery Location */}
            <FormField>
              <FormLabel required>Delivery Location</FormLabel>
              <Input
                value={formData.deliveryLocation}
                onChangeText={(value) => updateFormData('deliveryLocation', value)}
                placeholder="Enter delivery address or location"
                multiline
                numberOfLines={3}
                error={errors.deliveryLocation}
                leftIcon={<MapPin size={18} color={theme.colors.mutedForeground} />}
              />
              <FormMessage error={errors.deliveryLocation} />
            </FormField>

            {/* Urgency Selection */}
            <FormField>
              <FormLabel>Urgency</FormLabel>
              <View style={styles.urgencyContainer}>
                {(['standard', 'urgent', 'asap'] as const).map((urgency) => (
                  <TouchableOpacity
                    key={urgency}
                    onPress={() => updateFormData('urgency', urgency)}
                    style={[
                      styles.urgencyOption,
                      {
                        borderColor: formData.urgency === urgency 
                          ? theme.colors.primary 
                          : theme.colors.border,
                        backgroundColor: formData.urgency === urgency 
                          ? theme.colors.primary + '10' 
                          : 'transparent',
                      }
                    ]}
                    activeOpacity={0.7}
                  >
                    <Badge variant={getUrgencyBadgeVariant(urgency)} size="sm">
                      {getUrgencyLabel(urgency)}
                    </Badge>
                  </TouchableOpacity>
                ))}
              </View>
            </FormField>

            {/* Additional Requirements */}
            <FormField>
              <FormLabel>Additional Requirements</FormLabel>
              <Input
                value={formData.additionalRequirements}
                onChangeText={(value) => updateFormData('additionalRequirements', value)}
                placeholder="Any additional requirements or special instructions (optional)"
                multiline
                numberOfLines={4}
                leftIcon={<FileText size={18} color={theme.colors.mutedForeground} />}
              />
            </FormField>
          </FormSection>

          <FormActions align="space-between">
            {onCancel && (
              <Button
                title="Cancel"
                onPress={onCancel}
                variant="outline"
                size="lg"
                style={styles.cancelButton}
              />
            )}

            <Button
              title={isSubmitting ? 'Submitting...' : 'Submit Quotation'}
              onPress={handleSubmit}
              loading={isSubmitting}
              disabled={isSubmitting || formData.items.length === 0}
              variant="primary"
              size="lg"
              style={styles.submitButton}
              leftIcon={<Clock size={16} color={theme.colors.primaryForeground} />}
            />
          </FormActions>
        </Form>
      </Card>

      {/* Summary Card */}
      <Card variant="outlined" padding="md" style={styles.summaryCard}>
        <View style={styles.summaryHeader}>
          <Text style={[styles.summaryTitle, { color: theme.colors.foreground }]}>
            Quotation Summary
          </Text>
          <Badge variant={getUrgencyBadgeVariant(formData.urgency)} size="sm">
            {getUrgencyLabel(formData.urgency)}
          </Badge>
        </View>

        <Body2 color="mutedForeground" style={styles.summaryText}>
          {formData.items.length} product{formData.items.length !== 1 ? 's' : ''} requested
        </Body2>

        {formData.deliveryLocation && (
          <Body2 color="mutedForeground" style={styles.summaryText}>
            Delivery to: {formData.deliveryLocation.substring(0, 50)}
            {formData.deliveryLocation.length > 50 ? '...' : ''}
          </Body2>
        )}
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 32,
    marginTop: 16,
  },
  subtitle: {
    marginTop: 8,
  },
  card: {
    marginBottom: 16,
  },
  itemCard: {
    marginBottom: 16,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemTitle: {
    fontSize: 16,
    fontFamily: 'System',
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  removeButton: {
    padding: 4,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  quantityField: {
    flex: 2,
  },
  unitField: {
    flex: 1,
  },
  addButton: {
    marginTop: 8,
  },
  urgencyContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  urgencyOption: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  submitButton: {
    flex: 2,
  },
  summaryCard: {
    marginBottom: 32,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontFamily: 'System',
    fontWeight: '600',
  },
  summaryText: {
    marginBottom: 4,
  },
});

export default EnhancedQuotationForm;
