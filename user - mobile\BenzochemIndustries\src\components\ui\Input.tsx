import React, { useState, forwardRef } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  helperStyle?: TextStyle;
  variant?: 'default' | 'filled' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  required?: boolean;
  disabled?: boolean;
}

const Input = forwardRef<TextInput, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  helperStyle,
  variant = 'outline',
  size = 'md',
  required = false,
  disabled = false,
  ...props
}, ref) => {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: 8,
      borderWidth: 1,
      flexDirection: 'row',
      alignItems: 'center',
    };

    // Size styles
    const sizeStyles = {
      sm: {
        minHeight: 36,
        paddingHorizontal: 12,
      },
      md: {
        minHeight: 44,
        paddingHorizontal: 16,
      },
      lg: {
        minHeight: 52,
        paddingHorizontal: 20,
      },
    };

    // Variant styles
    const variantStyles = {
      default: {
        backgroundColor: theme.colors.background,
        borderColor: theme.colors.border,
      },
      filled: {
        backgroundColor: theme.colors.muted,
        borderColor: 'transparent',
      },
      outline: {
        backgroundColor: 'transparent',
        borderColor: theme.colors.border,
      },
    };

    // State styles
    const stateStyle = error ? {
      borderColor: theme.colors.destructive,
    } : isFocused ? {
      borderColor: theme.colors.primary,
      borderWidth: 2,
    } : {};

    const disabledStyle = disabled ? {
      opacity: 0.6,
      backgroundColor: theme.colors.muted,
    } : {};

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...stateStyle,
      ...disabledStyle,
    };
  };

  const getInputStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      flex: 1,
      fontFamily: theme.fonts.regular,
      fontSize: 16,
      color: theme.colors.foreground,
      paddingVertical: 0, // Remove default padding
    };

    const sizeStyles = {
      sm: {
        fontSize: 14,
      },
      md: {
        fontSize: 16,
      },
      lg: {
        fontSize: 18,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
    };
  };

  const getLabelStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.medium,
      fontSize: 14,
      color: theme.colors.foreground,
      marginBottom: 6,
    };
  };

  const getErrorStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.destructive,
      marginTop: 4,
    };
  };

  const getHelperStyle = (): TextStyle => {
    return {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.mutedForeground,
      marginTop: 4,
    };
  };

  return (
    <View style={containerStyle}>
      {label && (
        <Text style={[getLabelStyle(), labelStyle]}>
          {label}
          {required && <Text style={{ color: theme.colors.destructive }}> *</Text>}
        </Text>
      )}
      
      <View style={getContainerStyle()}>
        {leftIcon && (
          <View style={styles.leftIcon}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          ref={ref}
          style={[getInputStyle(), inputStyle]}
          placeholderTextColor={theme.colors.mutedForeground}
          onFocus={(e) => {
            setIsFocused(true);
            props.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            props.onBlur?.(e);
          }}
          editable={!disabled}
          {...props}
        />
        
        {rightIcon && (
          <TouchableOpacity style={styles.rightIcon}>
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text style={[getErrorStyle(), errorStyle]}>
          {error}
        </Text>
      )}
      
      {helperText && !error && (
        <Text style={[getHelperStyle(), helperStyle]}>
          {helperText}
        </Text>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  leftIcon: {
    marginRight: 12,
  },
  rightIcon: {
    marginLeft: 12,
  },
});

Input.displayName = 'Input';

export default Input;
