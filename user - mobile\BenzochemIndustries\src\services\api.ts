import { Product, ApiProduct, UserData, Collection, Quotation } from '../types';
import {
  ADMIN_API_CONFIG,
  WEB_API_CONFIG,
  REQUEST_CONFIG,
  ERROR_MESSAGES,
  GST_VERIFICATION_CONFIG,
  VA<PERSON><PERSON><PERSON><PERSON>_PATTERNS,
  API_ENDPOINTS,
  ENVIRONMENT
} from '../config';

// API client for communicating with the web app's API endpoints
class ApiClient {
  private baseUrl: string;
  private adminUrl: string;
  private apiKey: string;
  private timeout: number;

  constructor() {
    this.baseUrl = ENVIRONMENT.WEB_API_URL;
    this.adminUrl = ENVIRONMENT.ADMIN_API_URL;
    this.apiKey = ADMIN_API_CONFIG.API_KEY;
    this.timeout = REQUEST_CONFIG.TIMEOUT;

    console.log('API Client initialized with:');
    console.log('- Web API URL:', this.baseUrl);
    console.log('- Admin API URL:', this.adminUrl);
    console.log('- API Key configured:', !!this.apiKey);
    console.log('- Raw Config Values:');
    console.log('  - ADMIN_API_CONFIG.BASE_URL:', ADMIN_API_CONFIG.BASE_URL);
    console.log('  - ADMIN_API_CONFIG.API_KEY:', ADMIN_API_CONFIG.API_KEY);
    console.log('  - ENVIRONMENT.ADMIN_API_URL:', ENVIRONMENT.ADMIN_API_URL);
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    useAdmin = false
  ): Promise<{ success: boolean; data?: T; error?: string; pagination?: any }> {
    try {
      const url = `${useAdmin ? this.adminUrl : this.baseUrl}${endpoint}`;

      console.log('Making API request to:', url);
      console.log('Using admin API:', useAdmin);

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const headers = useAdmin ? REQUEST_CONFIG.ADMIN_HEADERS : REQUEST_CONFIG.DEFAULT_HEADERS;

      console.log('Request headers:', headers);

      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log('Response status:', response.status, response.statusText);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        
        // Handle specific HTTP status codes
        switch (response.status) {
          case 401:
            errorMessage = ERROR_MESSAGES.API.UNAUTHORIZED;
            break;
          case 403:
            errorMessage = ERROR_MESSAGES.API.FORBIDDEN;
            break;
          case 404:
            errorMessage = ERROR_MESSAGES.API.NOT_FOUND;
            break;
          case 500:
          case 502:
          case 503:
          case 504:
            errorMessage = ERROR_MESSAGES.NETWORK.SERVER_ERROR;
            break;
          default:
            try {
              const errorResult = await response.json();
              errorMessage = errorResult.error || errorResult.message || errorMessage;
            } catch {
              errorMessage = response.statusText || errorMessage;
            }
        }
        
        throw new Error(errorMessage);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      
      let errorMessage = ERROR_MESSAGES.NETWORK.CONNECTION_FAILED;
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = ERROR_MESSAGES.NETWORK.TIMEOUT;
        } else if (error.message.includes('Failed to fetch') || error.name === 'TypeError') {
          errorMessage = useAdmin 
            ? ERROR_MESSAGES.API.ADMIN_CONNECTION_FAILED
            : ERROR_MESSAGES.API.WEB_CONNECTION_FAILED;
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  // Products API
  async getProducts(params: {
    limit?: number;
    offset?: number;
    search?: string;
    collection?: string;
    featured?: boolean;
  } = {}) {
    const searchParams = new URLSearchParams();

    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.search) searchParams.set('search', params.search);
    if (params.collection) searchParams.set('collection', params.collection);
    if (params.featured) searchParams.set('featured', 'true');

    const query = searchParams.toString();
    const endpoint = `/products${query ? `?${query}` : ''}`;

    console.log('Making products API request to:', endpoint);
    console.log('Using admin API:', true);

    // Use admin API directly for products
    return this.makeRequest(endpoint, {}, true);
  }

  async getProductById(productId: string) {
    console.log('Making product by ID API request for:', productId);
    console.log('Using admin API:', true);

    // Use admin API directly for product details
    return this.makeRequest(`/products/${productId}`, {}, true);
  }

  async getFeaturedProducts(): Promise<Product[]> {
    try {
      const response = await this.getProducts({ featured: true, limit: 8 });
      if (response.success && response.data && Array.isArray(response.data)) {
        return (response.data as ApiProduct[]).map(this.transformApiProductToProduct);
      }
      return [];
    } catch (error) {
      console.error('Error fetching featured products:', error);
      return [];
    }
  }

  async searchProducts(query: string): Promise<Product[]> {
    try {
      const response = await this.getProducts({ search: query, limit: 20 });
      if (response.success && response.data && Array.isArray(response.data)) {
        return (response.data as ApiProduct[]).map(this.transformApiProductToProduct);
      }
      return [];
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  }

  async getProductsByCollection(collectionTitle: string): Promise<Product[]> {
    try {
      const response = await this.getProducts({ collection: collectionTitle, limit: 100 });
      if (response.success && response.data && Array.isArray(response.data)) {
        return (response.data as ApiProduct[]).map(this.transformApiProductToProduct);
      }
      return [];
    } catch (error) {
      console.error(`Error fetching products for collection ${collectionTitle}:`, error);
      return [];
    }
  }

  // Collections API
  async getCollections(params: {
    limit?: number;
    offset?: number;
    search?: string;
    visible?: boolean;
  } = {}) {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.search) searchParams.set('search', params.search);
    if (params.visible !== undefined) searchParams.set('visible', params.visible.toString());
    
    const query = searchParams.toString();
    const endpoint = `/collections${query ? `?${query}` : ''}`;
    
    return this.makeRequest(endpoint, {}, true); // Use admin API for collections
  }

  // Authentication API
  async login(email: string, password: string) {
    return this.makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phone?: string;
    businessName?: string;
    gstNumber?: string;
    legalNameOfBusiness?: string;
    tradeName?: string;
    dateOfRegistration?: string;
    constitutionOfBusiness?: string;
    taxpayerType?: string;
    principalPlaceOfBusiness?: string;
    natureOfCoreBusinessActivity?: string;
    gstStatus?: string;
    agreedToEmailMarketing?: boolean;
    agreedToSmsMarketing?: boolean;
  }) {
    return this.makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getUserByEmail(email: string) {
    return this.makeRequest(`/users?email=${encodeURIComponent(email)}`, {}, true);
  }

  async createUser(userData: {
    userId: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    businessName?: string;
    gstNumber?: string;
    legalNameOfBusiness?: string;
    tradeName?: string;
    dateOfRegistration?: string;
    constitutionOfBusiness?: string;
    taxpayerType?: string;
    principalPlaceOfBusiness?: string;
    natureOfCoreBusinessActivity?: string;
    gstStatus?: string;
    agreedToEmailMarketing?: boolean;
    agreedToSmsMarketing?: boolean;
  }) {
    return this.makeRequest('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    }, true);
  }

  // Quotations API
  async createQuotation(quotationData: {
    userId: string;
    userEmail: string;
    userName: string;
    userPhone?: string;
    businessName?: string;
    products: Array<{
      productId: string;
      productName: string;
      quantity: string;
      unit: string;
      specifications?: string;
    }>;
    additionalRequirements?: string;
    deliveryLocation?: string;
    urgency?: 'standard' | 'urgent' | 'asap';
  }) {
    return this.makeRequest('/quotations', {
      method: 'POST',
      body: JSON.stringify(quotationData),
    });
  }

  async getUserQuotations(userId: string, params: {
    limit?: number;
    offset?: number;
    status?: string;
  } = {}) {
    const searchParams = new URLSearchParams();
    searchParams.set('userId', userId);
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.status) searchParams.set('status', params.status);
    
    const query = searchParams.toString();
    return this.makeRequest(`/quotations?${query}`);
  }

  async getQuotation(quotationId: string) {
    return this.makeRequest(`/quotations/${quotationId}`);
  }

  // GST Verification
  async verifyGST(gstNumber: string): Promise<{ success: boolean; error?: string; data?: any }> {
    // Validate GST number format using config pattern
    if (!VALIDATION_PATTERNS.GST_NUMBER.test(gstNumber)) {
      return {
        success: false,
        error: ERROR_MESSAGES.VALIDATION.INVALID_GST,
      };
    }

    try {
      // Use web API endpoint for GST verification
      const response = await this.makeRequest(API_ENDPOINTS.WEB.GST_VERIFICATION, {
        method: 'POST',
        body: JSON.stringify({ gstNumber }),
      });
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: "GST verification service unavailable",
      };
    }
  }

  // Contact API
  async sendContactMessage(contactData: {
    firstName: string;
    lastName: string;
    email: string;
    company?: string;
    inquiryType?: string;
    message: string;
  }) {
    return this.makeRequest('/contact', {
      method: 'POST',
      body: JSON.stringify(contactData),
    });
  }

  // Search Suggestions API
  async getSearchSuggestions(query: string) {
    if (!query || query.trim().length < 2) {
      return { success: true, data: [] };
    }

    return this.makeRequest(`/search-suggestions?q=${encodeURIComponent(query.trim())}`);
  }

  // Search History API
  async getSearchHistory() {
    return this.makeRequest('/search-history');
  }

  async addToSearchHistory(query: string) {
    return this.makeRequest('/search-history', {
      method: 'POST',
      body: JSON.stringify({ query }),
    });
  }

  // Address Search API (using web API endpoint)
  async getAddressSuggestions(query: string) {
    if (!query || query.length < 3) {
      return { success: true, suggestions: [] };
    }

    return this.makeRequest(`/address-search?q=${encodeURIComponent(query)}`);
  }

  // Newsletter API
  async subscribeToNewsletter(email: string, firstName?: string) {
    return this.makeRequest('/send-newsletter', {
      method: 'POST',
      body: JSON.stringify({ email, firstName }),
    });
  }

  // Email Verification API
  async verifyEmail(email: string, token: string) {
    return this.makeRequest('/verify-email', {
      method: 'POST',
      body: JSON.stringify({ email, token }),
    });
  }

  // Phone Verification API
  async verifyPhone(phone: string, otp: string) {
    return this.makeRequest('/verify-phone', {
      method: 'POST',
      body: JSON.stringify({ phone, otp }),
    });
  }

  // Send Welcome Email API
  async sendWelcomeEmail(email: string, firstName: string) {
    return this.makeRequest('/send-welcome-email', {
      method: 'POST',
      body: JSON.stringify({ email, firstName }),
    });
  }

  // Send Account Status Email API
  async sendAccountStatusEmail(email: string, firstName: string, status: string) {
    return this.makeRequest('/send-account-status-email', {
      method: 'POST',
      body: JSON.stringify({ email, firstName, status }),
    });
  }

  // Send Quotation Email API
  async sendQuotationEmail(email: string, firstName: string, quotationId: string, type: 'created' | 'updated' | 'approved' | 'rejected') {
    return this.makeRequest('/send-quotation-email', {
      method: 'POST',
      body: JSON.stringify({ email, firstName, quotationId, type }),
    });
  }

  // Cookie Consent API
  async updateCookieConsent(preferences: any) {
    return this.makeRequest('/cookie-consent', {
      method: 'POST',
      body: JSON.stringify(preferences),
    });
  }

  // Link Consent API
  async linkConsent(consentData: any) {
    return this.makeRequest('/link-consent', {
      method: 'POST',
      body: JSON.stringify(consentData),
    });
  }

  // Google Places API for address autocomplete (fallback)
  async getPlaceSuggestions(input: string): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      const url = `${GOOGLE_PLACES_CONFIG.AUTOCOMPLETE_URL}?input=${encodeURIComponent(input)}&key=${GOOGLE_PLACES_CONFIG.API_KEY}&types=establishment&components=country:in`;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch place suggestions');
      }

      const result = await response.json();

      if (result.status === 'OK') {
        return {
          success: true,
          data: result.predictions || [],
        };
      } else {
        return {
          success: false,
          error: result.error_message || 'Failed to get place suggestions',
        };
      }
    } catch (error) {
      console.error('Error fetching place suggestions:', error);
      return {
        success: false,
        error: 'Unable to fetch place suggestions',
      };
    }
  }

  // Get place details by place ID
  async getPlaceDetails(placeId: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const url = `${GOOGLE_PLACES_CONFIG.DETAILS_URL}?place_id=${placeId}&key=${GOOGLE_PLACES_CONFIG.API_KEY}&fields=formatted_address,geometry,name`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch place details');
      }
      
      const result = await response.json();
      
      if (result.status === 'OK') {
        return {
          success: true,
          data: result.result,
        };
      } else {
        return {
          success: false,
          error: result.error_message || 'Failed to get place details',
        };
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
      return {
        success: false,
        error: 'Unable to fetch place details',
      };
    }
  }

  // Transform API product data to match the expected Product interface
  transformApiProductToProduct(apiProduct: ApiProduct): Product {
    return {
      id: apiProduct.id,
      title: apiProduct.title,
      description: apiProduct.description,
      descriptionHtml: apiProduct.description,
      tags: apiProduct.tags || [],
      quantity: apiProduct.quantity,
      collections: {
        edges: (apiProduct.collections || []).map((collection: string) => ({
          node: { title: collection }
        }))
      },
      images: {
        edges: (apiProduct.images || []).map((image: any) => ({
          node: { url: image.url || image }
        }))
      },
      media: {
        edges: (apiProduct.images || []).map((image: any, index: number) => ({
          node: {
            id: `media_${index}`,
            image: { url: image.url || image }
          }
        }))
      },
      priceRange: {
        minVariantPrice: {
          amount: apiProduct.priceRange?.minVariantPrice?.amount || '0.00',
          currencyCode: apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'
        },
        maxVariantPrice: {
          amount: apiProduct.priceRange?.maxVariantPrice?.amount || apiProduct.priceRange?.minVariantPrice?.amount || '0.00',
          currencyCode: apiProduct.priceRange?.maxVariantPrice?.currencyCode || apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'
        }
      },
      compareAtPriceRange: {
        minVariantPrice: {
          amount: '0.00',
          currencyCode: 'USD'
        },
        maxVariantPrice: {
          amount: '0.00',
          currencyCode: 'USD'
        }
      },
      metafields: [
        ...(apiProduct.purity ? [{
          id: 'purity',
          key: 'purity',
          namespace: 'chemical',
          value: apiProduct.purity,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.casNumber ? [{
          id: 'cas_number',
          key: 'cas_number',
          namespace: 'chemical',
          value: apiProduct.casNumber,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.molecularFormula ? [{
          id: 'molecular_formula',
          key: 'molecular_formula',
          namespace: 'chemical',
          value: apiProduct.molecularFormula,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.molecularWeight ? [{
          id: 'molecular_weight',
          key: 'molecular_weight',
          namespace: 'chemical',
          value: apiProduct.molecularWeight,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.appearance ? [{
          id: 'appearance',
          key: 'appearance',
          namespace: 'chemical',
          value: apiProduct.appearance,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.solubility ? [{
          id: 'solubility',
          key: 'solubility',
          namespace: 'chemical',
          value: apiProduct.solubility,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.phValue ? [{
          id: 'ph_value',
          key: 'ph_value',
          namespace: 'chemical',
          value: apiProduct.phValue,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.hsnNumber ? [{
          id: 'hsn_number',
          key: 'hsn_number',
          namespace: 'chemical',
          value: apiProduct.hsnNumber,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.chemicalName ? [{
          id: 'chemical_name',
          key: 'chemical_name',
          namespace: 'chemical',
          value: apiProduct.chemicalName,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.packaging ? [{
          id: 'packaging',
          key: 'packaging',
          namespace: 'chemical',
          value: JSON.stringify(apiProduct.packaging),
          type: 'json'
        }] : []),
        ...(apiProduct.features ? [{
          id: 'features',
          key: 'features',
          namespace: 'chemical',
          value: JSON.stringify(apiProduct.features),
          type: 'json'
        }] : []),
        ...(apiProduct.applications ? [{
          id: 'applications',
          key: 'applications',
          namespace: 'chemical',
          value: JSON.stringify(apiProduct.applications),
          type: 'json'
        }] : []),
        ...(apiProduct.applicationDetails ? [{
          id: 'application_details',
          key: 'application_details',
          namespace: 'chemical',
          value: JSON.stringify(apiProduct.applicationDetails),
          type: 'json'
        }] : [])
      ]
    };
  }
}

// Export singleton instance
export const apiClient = new ApiClient();