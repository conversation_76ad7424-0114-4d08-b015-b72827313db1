/**
 * Configuration for Benzochem Industries Mobile App
 * Based on environment variables from .env file
 */

import Config from 'react-native-config';

// Debug: Log what react-native-config is loading
console.log('React Native Config loaded:');
console.log('- ADMIN_API_BASE_URL:', Config.ADMIN_API_BASE_URL);
console.log('- ADMIN_API_KEY:', Config.ADMIN_API_KEY ? 'Present' : 'Missing');
console.log('- WEB_API_BASE_URL:', Config.WEB_API_BASE_URL);

// Helper function to parse string to number with fallback
const parseNumber = (value: string | undefined, fallback: number): number => {
  if (!value) return fallback;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
};

// Helper function to parse string to boolean with fallback
const parseBoolean = (value: string | undefined, fallback: boolean): boolean => {
  if (!value) return fallback;
  return value.toLowerCase() === 'true';
};

// =============================================================================
// ADMIN API CONFIGURATION
// =============================================================================
export const ADMIN_API_CONFIG = {
  // URL of the admin backend API (where the admin project is running)
  BASE_URL: Config.ADMIN_API_BASE_URL || 'https://apibenzochem.vercel.app',
  API_VERSION: Config.ADMIN_API_VERSION || 'v1',

  // API key for accessing the admin backend
  // This API key needs the following permissions:
  // - users:read, users:write
  // - products:read
  // - collections:read
  // - quotations:read, quotations:write
  API_KEY: Config.ADMIN_API_KEY || 'bzk_live_nGkfHjbSjzjy8Ex76T32F54tcx8RgRUQ',

  // Full admin API URL
  get FULL_URL() {
    return `${this.BASE_URL}/api/${this.API_VERSION}`;
  }
};

// =============================================================================
// WEB API CONFIGURATION
// =============================================================================
export const WEB_API_CONFIG = {
  // URL of the web frontend API (Next.js app API routes)
  BASE_URL: Config.WEB_API_BASE_URL || 'http://localhost:3000',
  
  // Full web API URL
  get FULL_URL() {
    return `${this.BASE_URL}/api`;
  }
};

// =============================================================================
// APPLICATION CONFIGURATION
// =============================================================================
export const APP_CONFIG = {
  // Authentication URLs (Admin API-based)
  SIGN_IN_URL: Config.SIGN_IN_URL || '/login',
  SIGN_UP_URL: Config.SIGN_UP_URL || '/register',
  AFTER_SIGN_IN_URL: Config.AFTER_SIGN_IN_URL || '/account',
  AFTER_SIGN_UP_URL: Config.AFTER_SIGN_UP_URL || '/account',
  
  // App metadata
  NAME: Config.APP_NAME || 'Benzochem Industries',
  VERSION: Config.APP_VERSION || '1.0.0',
  BUNDLE_ID: Config.APP_BUNDLE_ID || 'com.benzochemindustries',
};

// =============================================================================
// RAPIDAPI GST VERIFICATION SERVICE
// =============================================================================
export const GST_VERIFICATION_CONFIG = {
  // RapidAPI GST Verification API Credentials
  RAPIDAPI_KEY: Config.RAPIDAPI_KEY || '',
  RAPIDAPI_HOST: Config.RAPIDAPI_GST_HOST || 'gst-return-status.p.rapidapi.com',
  BASE_URL: Config.RAPIDAPI_GST_BASE_URL || 'https://gst-return-status.p.rapidapi.com/free',
  
  // Headers for RapidAPI requests
  get HEADERS() {
    return {
      'X-RapidAPI-Key': this.RAPIDAPI_KEY,
      'X-RapidAPI-Host': this.RAPIDAPI_HOST,
      'Content-Type': 'application/json',
    };
  }
};

// =============================================================================
// GOOGLE PLACES API CONFIGURATION
// =============================================================================
export const GOOGLE_PLACES_CONFIG = {
  // Google Places API Configuration
  API_KEY: Config.GOOGLE_PLACES_API_KEY || '',
  BASE_URL: Config.GOOGLE_PLACES_BASE_URL || 'https://maps.googleapis.com/maps/api/place',
  
  // Common parameters
  get AUTOCOMPLETE_URL() {
    return `${this.BASE_URL}/autocomplete/json`;
  },
  
  get DETAILS_URL() {
    return `${this.BASE_URL}/details/json`;
  }
};

// =============================================================================
// API ENDPOINTS
// =============================================================================
export const API_ENDPOINTS = {
  // Admin API endpoints
  ADMIN: {
    USERS: '/users',
    PRODUCTS: '/products',
    COLLECTIONS: '/collections',
    QUOTATIONS: '/quotations',
  },
  
  // Web API endpoints
  WEB: {
    AUTH: {
      LOGIN: '/auth/login',
      REGISTER: '/auth/register',
      LOGOUT: '/auth/logout',
      FORGOT_PASSWORD: '/auth/forgot-password',
      RESET_PASSWORD: '/auth/reset-password',
    },
    PRODUCTS: '/products',
    QUOTATIONS: '/quotations',
    CONTACT: '/contact',
    SEARCH_SUGGESTIONS: '/search-suggestions',
    SEARCH_HISTORY: '/search-history',
    ADDRESS_SEARCH: '/address-search',
    GST_VERIFICATION: '/verify-gst-details',
    EMAIL_VERIFICATION: '/verify-email',
    PHONE_VERIFICATION: '/verify-phone',
    NEWSLETTER: '/send-newsletter',
    WELCOME_EMAIL: '/send-welcome-email',
    ACCOUNT_STATUS_EMAIL: '/send-account-status-email',
    QUOTATION_EMAIL: '/send-quotation-email',
    COOKIE_CONSENT: '/cookie-consent',
    LINK_CONSENT: '/link-consent',
    INIT_COLLECTIONS: '/init-collections',
  }
};

// =============================================================================
// REQUEST CONFIGURATION
// =============================================================================
export const REQUEST_CONFIG = {
  // Default timeout for API requests (in milliseconds)
  TIMEOUT: parseNumber(Config.REQUEST_TIMEOUT, 30000),
  
  // Retry configuration
  MAX_RETRIES: parseNumber(Config.MAX_RETRIES, 3),
  RETRY_DELAY: parseNumber(Config.RETRY_DELAY, 1000),
  
  // Default headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // Admin API headers
  get ADMIN_HEADERS() {
    return {
      ...this.DEFAULT_HEADERS,
      'X-API-Key': ADMIN_API_CONFIG.API_KEY,
    };
  }
};

// =============================================================================
// ENVIRONMENT DETECTION
// =============================================================================
export const ENVIRONMENT = {
  isDevelopment: __DEV__,
  isProduction: !__DEV__,
  
  // Development features
  debugLogging: parseBoolean(Config.DEBUG_LOGGING, __DEV__),
  enableDevFeatures: parseBoolean(Config.ENABLE_DEV_FEATURES, __DEV__),
  
  // Security
  disableSSLVerification: parseBoolean(Config.DISABLE_SSL_VERIFICATION, false),
  
  // API URLs based on environment
  get ADMIN_API_URL() {
    return ADMIN_API_CONFIG.FULL_URL;
  },
  
  get WEB_API_URL() {
    return WEB_API_CONFIG.FULL_URL;
  }
};

// =============================================================================
// VALIDATION PATTERNS
// =============================================================================
export const VALIDATION_PATTERNS = {
  // GST number validation pattern
  GST_NUMBER: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
  
  // Email validation pattern
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  
  // Phone number validation pattern (Indian)
  PHONE: /^[6-9]\d{9}$/,
  
  // PAN number validation pattern
  PAN: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
};

// =============================================================================
// BUSINESS CONSTANTS
// =============================================================================
export const BUSINESS_CONSTANTS = {
  // Currency
  DEFAULT_CURRENCY: Config.DEFAULT_CURRENCY || 'INR',
  CURRENCY_SYMBOL: Config.CURRENCY_SYMBOL || '₹',
  
  // Quotation urgency levels
  URGENCY_LEVELS: {
    STANDARD: 'standard',
    URGENT: 'urgent',
    ASAP: 'asap',
  },
  
  // Product units
  PRODUCT_UNITS: [
    'kg', 'g', 'mg', 'ton', 'quintal',
    'liter', 'ml', 'gallon',
    'piece', 'box', 'carton', 'bag',
    'meter', 'cm', 'mm', 'inch', 'feet'
  ],
  
  // GST status options
  GST_STATUS_OPTIONS: [
    'Active',
    'Inactive',
    'Suspended',
    'Cancelled',
    'Provisional'
  ],
  
  // Constitution of business options
  CONSTITUTION_OPTIONS: [
    'Proprietorship',
    'Partnership',
    'Private Limited Company',
    'Public Limited Company',
    'LLP',
    'Trust',
    'Society',
    'Others'
  ],
  
  // Taxpayer type options
  TAXPAYER_TYPE_OPTIONS: [
    'Regular',
    'Composition',
    'Casual',
    'Non-resident',
    'Input Service Distributor',
    'TDS',
    'TCS'
  ]
};

// =============================================================================
// ERROR MESSAGES
// =============================================================================
export const ERROR_MESSAGES = {
  NETWORK: {
    CONNECTION_FAILED: 'Unable to connect to server. Please check your internet connection.',
    TIMEOUT: 'Request timed out. Please try again.',
    SERVER_ERROR: 'Server error occurred. Please try again later.',
  },
  
  VALIDATION: {
    REQUIRED_FIELD: 'This field is required',
    INVALID_EMAIL: 'Please enter a valid email address',
    INVALID_PHONE: 'Please enter a valid phone number',
    INVALID_GST: 'Please enter a valid GST number',
    PASSWORD_TOO_SHORT: 'Password must be at least 8 characters long',
  },
  
  AUTH: {
    INVALID_CREDENTIALS: 'Invalid email or password',
    USER_NOT_FOUND: 'User not found',
    EMAIL_ALREADY_EXISTS: 'Email already exists',
    REGISTRATION_FAILED: 'Registration failed. Please try again.',
  },
  
  API: {
    ADMIN_CONNECTION_FAILED: 'Cannot connect to admin API. Make sure admin project is running on port 3001',
    WEB_CONNECTION_FAILED: 'Cannot connect to web API. Make sure web project is running on port 3000',
    UNAUTHORIZED: 'Unauthorized access. Please check your API key.',
    FORBIDDEN: 'Access forbidden. Insufficient permissions.',
    NOT_FOUND: 'Resource not found.',
  }
};

// =============================================================================
// SUCCESS MESSAGES
// =============================================================================
export const SUCCESS_MESSAGES = {
  AUTH: {
    LOGIN_SUCCESS: 'Login successful',
    REGISTRATION_SUCCESS: 'Registration successful',
    LOGOUT_SUCCESS: 'Logout successful',
  },
  
  QUOTATION: {
    CREATED: 'Quotation request submitted successfully',
    UPDATED: 'Quotation updated successfully',
  },
  
  PROFILE: {
    UPDATED: 'Profile updated successfully',
  },
  
  GST: {
    VERIFIED: 'GST number verified successfully',
  }
};

// =============================================================================
// STORAGE KEYS
// =============================================================================
export const STORAGE_KEYS = {
  // User data (non-sensitive)
  USER_PREFERENCES: 'user_preferences',
  THEME_MODE: 'theme_mode',
  RECENT_SEARCHES: 'recent_searches',
  LANGUAGE: 'language',
  
  // App state
  ONBOARDING_COMPLETED: 'onboarding_completed',
  LAST_APP_VERSION: 'last_app_version',
  
  // Cache keys
  PRODUCTS_CACHE: 'products_cache',
  COLLECTIONS_CACHE: 'collections_cache',
  
  // Note: Sensitive data like auth tokens should be stored in secure storage
  // and not in AsyncStorage
};

// =============================================================================
// CACHE CONFIGURATION
// =============================================================================
export const CACHE_CONFIG = {
  // Cache expiration times (in milliseconds)
  PRODUCTS_CACHE_DURATION: parseNumber(Config.PRODUCTS_CACHE_DURATION, 5 * 60 * 1000), // 5 minutes
  COLLECTIONS_CACHE_DURATION: parseNumber(Config.COLLECTIONS_CACHE_DURATION, 10 * 60 * 1000), // 10 minutes
  USER_CACHE_DURATION: parseNumber(Config.USER_CACHE_DURATION, 30 * 60 * 1000), // 30 minutes
  
  // Maximum cache sizes
  MAX_PRODUCTS_CACHE_SIZE: parseNumber(Config.MAX_PRODUCTS_CACHE_SIZE, 100),
  MAX_SEARCH_HISTORY_SIZE: parseNumber(Config.MAX_SEARCH_HISTORY_SIZE, 10),
  MAX_RECENT_SEARCHES_SIZE: parseNumber(Config.MAX_RECENT_SEARCHES_SIZE, 10),
};

// =============================================================================
// DEVELOPMENT UTILITIES
// =============================================================================
export const DEV_UTILS = {
  // Logging utility that respects environment settings
  log: (...args: any[]) => {
    if (ENVIRONMENT.debugLogging) {
      console.log('[BenzochemApp]', ...args);
    }
  },
  
  // Warning utility
  warn: (...args: any[]) => {
    if (ENVIRONMENT.debugLogging) {
      console.warn('[BenzochemApp]', ...args);
    }
  },
  
  // Error utility
  error: (...args: any[]) => {
    if (ENVIRONMENT.debugLogging) {
      console.error('[BenzochemApp]', ...args);
    }
  },
  
  // Environment info
  getEnvironmentInfo: () => ({
    isDevelopment: ENVIRONMENT.isDevelopment,
    isProduction: ENVIRONMENT.isProduction,
    debugLogging: ENVIRONMENT.debugLogging,
    enableDevFeatures: ENVIRONMENT.enableDevFeatures,
    adminApiUrl: ENVIRONMENT.ADMIN_API_URL,
    webApiUrl: ENVIRONMENT.WEB_API_URL,
    appVersion: APP_CONFIG.VERSION,
    bundleId: APP_CONFIG.BUNDLE_ID,
  })
};

// =============================================================================
// EXPORT DEFAULT CONFIG
// =============================================================================
export default {
  ADMIN_API_CONFIG,
  WEB_API_CONFIG,
  APP_CONFIG,
  GST_VERIFICATION_CONFIG,
  GOOGLE_PLACES_CONFIG,
  API_ENDPOINTS,
  REQUEST_CONFIG,
  ENVIRONMENT,
  VALIDATION_PATTERNS,
  BUSINESS_CONSTANTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  STORAGE_KEYS,
  CACHE_CONFIG,
  DEV_UTILS,
};